import React, { useCallback } from 'react';
import { Draggable } from 'react-beautiful-dnd';
import classnames from 'classnames';

import styles from './EcnRoleItem.scss';
import Ripple from '../../../../../../../common/components/utils/Ripple';
import Icon from '../../../../../../../common/components/utils/Icon';
import TextField from '../../../../../../../common/components/containers/EntityForm/fields/TextField';
import useT from '../../../../../../../common/components/utils/Translations/useT';
import StatusField from '../../../../../../../common/components/containers/EntityForm/fields/StatusField';
import { Active } from '../../../../../../../model/Statuses';
import { IEcnRoleItem } from '../../../../../../../common/abstract/EContent/IEContentLibrary';

const EcnRoleItem: React.FC<{
  item: IEcnRoleItem;
  index: number;
  isDraggable?: boolean;
  wrapClassName?: string;
  lastItem?: boolean;
  errorMessage?: any;
}> = ({ index, item, isDraggable, wrapClassName, lastItem, errorMessage }) => {
  const t = useT();

  const renderLink = useCallback(
    () => (
      <Ripple childRefProp="innerRef">
        <>
          <TextField
            noLabel
            columns={4}
            errorMessage={errorMessage}
            label={t('Role')}
            maxLength={70}
            name={`ecnRoles[${index}].name`}
            required={!index || !lastItem}
          />
          <StatusField
            buttonClassNames={lastItem ? styles.textMuted : ''}
            columns={4}
            defaultValue={Active.value}
            name={`ecnRoles[${index}].status`}
            required={!index || !lastItem}
            withTitle={false}
          />
        </>
      </Ripple>
    ),
    [t, index, lastItem, errorMessage],
  );

  const renderDraggableItem = useCallback(
    () => (
      <Draggable
        key={`role-${index}`}
        draggableId={(index as number).toString()}
        index={index}
      >
        {provided => (
          <div
            key={index}
            ref={provided.innerRef}
            {...provided.draggableProps}
            {...provided.dragHandleProps}
            className={classnames(styles.linkWrap, wrapClassName)}
            // eslint-disable-next-line react/forbid-dom-props
            style={{ ...provided.draggableProps.style }}
          >
            <Icon
              badge="drag_indicator"
              className={styles.linkDragIcon}
              name="material-icons"
              type="material"
            />
            {renderLink()}
          </div>
        )}
      </Draggable>
    ),
    [index, renderLink, wrapClassName],
  );

  return isDraggable ? renderDraggableItem() : renderLink();
};

export default EcnRoleItem;
