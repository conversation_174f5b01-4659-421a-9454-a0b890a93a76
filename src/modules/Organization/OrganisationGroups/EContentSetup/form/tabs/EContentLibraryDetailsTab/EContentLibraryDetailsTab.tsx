import React, { useCallback, useMemo } from 'react';
import { isEmpty } from 'lodash';

import { IEContentLibraryForm } from '../../EContentLibraryForm';
import useT from '../../../../../../../common/components/utils/Translations/useT';
import EntityForm from '../../../../../../../common/components/containers/EntityForm';
import EntityFormFieldSet from '../../../../../../../common/components/containers/EntityForm/EntityFormFieldSet';
import EntityNameField from '../../../../../../../common/components/containers/EntityForm/fields/EntityNameField';
import StatusWithDraftField from '../../../../../../../common/components/containers/EntityForm/fields/StatusWithDraftField';
import TextAreaField from '../../../../../../../common/components/containers/EntityForm/fields/TextAreaField';
import CheckboxWithConfirmField from '../../../../../../../common/components/containers/EntityForm/fields/CheckboxWithConfirmField';
import styles from './EContentLibraryDetailsTab.scss';
import DependsOnField from '../../../../../../../common/components/containers/EntityForm/DependsOnField';
import EcnRolesField from './EcnRolesField';

const EContentLibraryDetailsTab: React.FC<IEContentLibraryForm> = props => {
  const { entity, onGoBack, onSubmit } = props;

  const isNew = useMemo(() => !entity?.id, [entity]);

  const t = useT();

  const _onSubmit = useCallback(
    ({
      id,
      organisationGroupId,
      name,
      description,
      status,
      hasProcessManagement,
      ecnRoles,
    }) => {
      const _ecnRoles = ecnRoles.filter(x => !isEmpty(x.name));
      return onSubmit({
        id,
        organisationGroupId,
        name,
        description,
        status,
        hasProcessManagement,
        ecnRoles: _ecnRoles,
      });
    },
    [onSubmit],
  );

  const setFieldToEmpty = useCallback(
    fieldName => ({ setFieldValue }) => setFieldValue(fieldName, []),
    [],
  );

  return (
    <EntityForm
      entity={entity}
      title={t('Details')}
      onCancel={isNew ? onGoBack : undefined}
      onGoBack={isNew ? undefined : onGoBack}
      onSubmit={_onSubmit}
    >
      <EntityFormFieldSet>
        <EntityNameField columns={4} label={t('Library Name')} />
        <StatusWithDraftField columns={4} />
      </EntityFormFieldSet>
      <EntityFormFieldSet>
        <TextAreaField
          columns={1}
          label={t('Description')}
          name="description"
        />
      </EntityFormFieldSet>
      <EntityFormFieldSet>
        <CheckboxWithConfirmField
          className={styles.checkbox}
          columns={1}
          label={t('Process Management')}
          name="hasProcessManagement"
          popupText={t('Added role(s) will be removed, proceed?')}
          onUncheck={setFieldToEmpty('ecnRoles')}
        />
      </EntityFormFieldSet>
      <DependsOnField fieldName="hasProcessManagement">
        {hasProcessManagement =>
          hasProcessManagement ? <EcnRolesField /> : null
        }
      </DependsOnField>
    </EntityForm>
  );
};

export default EContentLibraryDetailsTab;
