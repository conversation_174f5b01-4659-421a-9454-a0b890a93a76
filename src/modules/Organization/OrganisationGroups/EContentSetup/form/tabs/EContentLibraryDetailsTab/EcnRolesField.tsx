import React, { useCallback, useEffect, useMemo } from 'react';
import { isEmpty, map } from 'lodash';
import { DragDropContext, Droppable } from 'react-beautiful-dnd';
import classNames from 'classnames';

import EcnRoleItem from './EcnRoleItem';
import { IEntityFieldBasicProps } from '../../../../../../../common/components/containers/EntityForm/internal/EntityField';
import { Active } from '../../../../../../../model/Statuses';
import useEntityFormContext from '../../../../../../../common/components/containers/EntityForm/internal/useEntityFormContext';
import styles from './EcnRoleItem.scss';
import useT from '../../../../../../../common/components/utils/Translations/useT';
import { IEcnRoleItem } from '../../../../../../../common/abstract/EContent/IEContentLibrary';
import AnimatedTitle from '../../../../../../../common/components/controls/base/AnimatedTitle';

export type TStatus = { name: string; value: string };

export interface IEcnRolesField
  extends Partial<IEntityFieldBasicProps<string | unknown>> {
  isNew?: boolean;
  defaultValue?: string;
  hasOnlyActiveStatus?: boolean;
  hasOmitDeleteStatus?: boolean;
  label?: string;
  options?: Array<TStatus>;
}

const EcnRolesField: React.FC<IEcnRolesField> = () => {
  const t = useT();
  const { values, setFieldValue } = useEntityFormContext();

  const tempItem = {
    id: null,
    name: '',
    status: Active.value,
    sequence: 1,
  };

  const ecnRoles = useMemo(
    () =>
      !values.ecnRoles || isEmpty(values.ecnRoles)
        ? [tempItem]
        : map(values.ecnRoles, x => ({
            ...tempItem,
            ...x,
          })),
    [values.ecnRoles, tempItem],
  );

  useEffect(() => {
    if (!isEmpty(ecnRoles[ecnRoles.length - 1].name)) {
      setFieldValue('ecnRoles', [
        ...ecnRoles,
        {
          ...tempItem,
          sequence: (ecnRoles[ecnRoles.length - 1].sequence ?? 1) + 1,
        },
      ]);
    }
  }, [ecnRoles, setFieldValue, tempItem]);

  const moveItemBySequence = (
    arr: IEcnRoleItem[],
    fromSequence: number,
    toSequence: number,
  ) => {
    const sorted = [...arr].sort((a, b) => a.sequence - b.sequence);
    const movedItem = sorted.find(item => item.sequence === fromSequence);
    if (!movedItem) return arr;

    const filtered = sorted.filter(item => item.sequence !== fromSequence);

    const insertIndex = filtered.findIndex(
      item => item.sequence === toSequence,
    );
    filtered.splice(insertIndex, 0, movedItem);

    return filtered.map((item, index) => ({
      ...item,
      sequence: index + 1,
    }));
  };

  const renderItem = useCallback(
    (item: IEcnRoleItem, index) => {
      const showError =
        ecnRoles.filter(
          x =>
            !isEmpty(x.name) &&
            x.name?.toLowerCase() === item.name?.toLowerCase(),
        )?.length > 1;
      return (
        <EcnRoleItem
          key={index}
          isDraggable
          errorMessage={showError ? t('Role should unique') : null}
          index={index}
          item={item}
          lastItem={index > 0 && index === ecnRoles.length - 1}
        />
      );
    },
    [ecnRoles, t],
  );

  const handleDragEnd = useCallback(
    result => {
      const { source, destination } = result;
      const fromSequence = ecnRoles[source.index]?.sequence;
      const toSequence = ecnRoles[destination.index]?.sequence;
      const newArr = moveItemBySequence(ecnRoles, fromSequence, toSequence);
      setFieldValue('ecnRoles', newArr);
    },
    [ecnRoles, setFieldValue],
  );

  return (
    <DragDropContext onDragEnd={handleDragEnd}>
      <Droppable droppableId="droppable-links">
        {provided => (
          <div ref={provided.innerRef} {...provided.droppableProps}>
            <div className={classNames(styles.processManagementWrapper)}>
              <div className="col-lg-3 col-md-6 col-sm-12 col-xs-12">
                <AnimatedTitle
                  className={classNames(styles.animatedTitle, 'mb-0')}
                  placeholder="Role"
                />
              </div>
              <div className="col-lg-3 col-md-6 col-sm-12 col-xs-12">
                <AnimatedTitle
                  className={classNames(styles.animatedTitle, 'mb-0 ml-5')}
                  placeholder="Status"
                />
              </div>
            </div>
            {map(ecnRoles, renderItem)}
            {provided.placeholder}
          </div>
        )}
      </Droppable>
    </DragDropContext>
  );
};

export default EcnRolesField;
