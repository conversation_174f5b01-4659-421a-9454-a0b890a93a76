import React, { use<PERSON>allback, useMemo } from 'react';
import { useHistory, useRouteMatch } from 'react-router-dom';
import { map } from 'lodash';

import { TLearningPlanStatus } from '../../../../../../../common/abstract/OrganisationGroup/ProgramGroups/ILearningPlan';
import EntityForm from '../../../../../../../common/components/containers/EntityForm';
import EntityFormFieldSet from '../../../../../../../common/components/containers/EntityForm/EntityFormFieldSet';
import EntityNameField from '../../../../../../../common/components/containers/EntityForm/fields/EntityNameField';
import LearningPlanStatusField from '../../../../../../../common/components/forms/LearningTaskPlanTree/LearningPlanStatusField';
import Box, {
  BoxSizeWrapper,
} from '../../../../../../../common/components/other/Box';
import useT from '../../../../../../../common/components/utils/Translations/useT';
import LearningPlanStatus from '../../../../../../../model/LearningPlanStatus';

interface ILearningPlan {
  id?: number;
  name: string;
  learningStatus: TLearningPlanStatus;
  subjectId: number;
  isSubmissionEnabled?: boolean;
  tasksCount?: [{ status: TLearningPlanStatus; count: number }];
}

const LearningPlanForm: React.FC<{
  title: string;
  onSubmit: (values: ILearningPlan) => void;
  node?: ILearningPlan;
  onCancel: () => void;
  parentNode?: { id: number };
}> = ({ onCancel, title, node, onSubmit, parentNode }) => {
  const t = useT();
  const { push } = useHistory();
  const { url } = useRouteMatch();
  const tasksCount = useMemo(() => node?.tasksCount || [], [node]);
  const _entity = useMemo(
    () =>
      node && node.id
        ? { ...node }
        : {
            learningStatus: LearningPlanStatus.Draft.value,
            name: '',
            subjectId: parentNode?.id as number,
          },
    [node, parentNode],
  );

  const _onSubmit = useCallback(
    (values: ILearningPlan) =>
      onSubmit({
        id: values.id,
        name: values.name,
        learningStatus: values.learningStatus,
        subjectId: values.subjectId,
      }),
    [onSubmit],
  );

  const handleTasksClick = useCallback(() => {
    push(`${url}/tasks`);
  }, [url, push]);

  const isNew = useMemo(() => !node?.id, [node]);
  return (
    <>
      <EntityForm
        columns={4}
        entity={_entity}
        entityName="LearningPlan"
        onCancel={isNew ? onCancel : undefined}
        onGoBack={isNew ? undefined : onCancel}
        onSubmit={_onSubmit}
      >
        <EntityFormFieldSet>
          <EntityNameField columns={3} label={t('#{title} Name', { title })} />
          <LearningPlanStatusField
            omitCompleted
            columns={3}
            label={t('Status')}
          />
        </EntityFormFieldSet>
      </EntityForm>

      <BoxSizeWrapper>
        <div className="row mt-10">
          <Box title={t('Tasks')} onClick={handleTasksClick}>
            {tasksCount &&
              tasksCount.length > 0 &&
              map(
                tasksCount
                  .slice()
                  .sort(
                    (a, b) =>
                      LearningPlanStatus.BasicByValue[a.status]?.id -
                      LearningPlanStatus.BasicByValue[b.status]?.id,
                  ),
                (item, key) => (
                  <div key={key}>
                    {t(`#{count} #{task} (#{status})`, {
                      count: item.count,
                      task: item.count > 1 ? 'tasks' : 'task',
                      status:
                        LearningPlanStatus.BasicByValue[item.status]?.name ||
                        '',
                    })}
                  </div>
                ),
              )}
            {tasksCount.length === 0 && <div>{t('0 task')}</div>}
          </Box>
        </div>
      </BoxSizeWrapper>
    </>
  );
};

export default LearningPlanForm;
