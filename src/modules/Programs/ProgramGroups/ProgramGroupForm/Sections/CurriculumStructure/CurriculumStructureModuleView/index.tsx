import React, { useCallback, useContext, useMemo, memo, useState } from 'react';
import { isNumber } from 'lodash';
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';
import { useQuery } from 'react-apollo';

import useT from '../../../../../../../common/components/utils/Translations/useT';
import useProgramGroupTypes from '../../../../../../../common/data/hooks/useProgramGroupTypes';
import { ProgramGroupFormContext } from '../../../ProgramGroupForm';
import columns, { ICurriculumStructureTable } from './columns';
import { IFilterBarComponentProps } from '../../../../../../../common/components/controls/FilterBar/FilterBar';
import GqlFullCrudTable from '../../../../../../../common/components/dataViews/GqlFullCrudTable';
import getCurriculumStructureGql from '../data/getCurriculumStructure.graphql';
import getCurriculumStructureCountGql from '../data/getCurriculumStructureCount.graphql';
import CurriculumStructureViewSelector from '../../../../../../../common/components/controls/FilterBar/CurriculumStructureViewSelector';
import ProgramStructureLPTreeSelector from '../../../../../../../common/components/controls/FilterBar/ProgramStructureLPTreeSelector';
import usePaginationRouting from '../../../../../../../common/data/hooks/usePaginationRouting';
import RoundedPrimaryButton from '../../../../../../../common/components/controls/RoundedPrimaryButton';
import Spinner from '../../../../../../../common/components/utils/Spinner';
import { PDF_TABLE_STYLES } from '../CurriculumStructure';

function CurriculumStructureModuleView({
  programGroupTypeId,
  programGroupName,
  fLoading,
  defaultFilterValues,
  onDefaultFilterValuesChange,
}) {
  const t = useT();
  const { getTitle } = useProgramGroupTypes(programGroupTypeId);
  const programName = getTitle('programName');
  const subjectName = getTitle('activityName');
  const learningPlanName = getTitle('learningPlan');

  const { pageNumber, pageSize } = usePaginationRouting();

  const { id: programGroupId } = useContext(ProgramGroupFormContext) || {};

  const _columns = useMemo(
    () =>
      columns(
        programName,
        subjectName,
        learningPlanName,
        defaultFilterValues.view,
      ),
    [programName, subjectName, learningPlanName, defaultFilterValues.view],
  );

  const onFilterChanged = useCallback((values, name) => {
    if (name === 'view') {
      return { ...values, programStructureLP: [] };
    }
    return { ...values };
  }, []);

  const programStructureLPFilter = useCallback(
    props => (
      <ProgramStructureLPTreeSelector
        {...props}
        initialSelectAll
        programGroupId={programGroupId}
        programGroupName={programGroupName}
        title="Program Structure"
      />
    ),
    [programGroupId, programGroupName],
  );

  const filterComponent = useMemo(() => {
    const component = {
      view: CurriculumStructureViewSelector,
      programStructureLP: programStructureLPFilter,
    };

    return component as {
      [key: string]: React.ComponentType<IFilterBarComponentProps<any>>;
    };
  }, [programStructureLPFilter]);

  const onFilterChange = useCallback(
    filters => {
      onDefaultFilterValuesChange(filters);
    },
    [onDefaultFilterValuesChange],
  );

  const cookQueryVariables = useCallback(
    ({ programStructureLP, ...values }) => ({
      ...values,
      programStructureLP:
        programStructureLP
          ?.filter(({ id }) => typeof id !== 'string')
          .map(item => item.id) || [],
    }),
    [],
  );

  const [items, setItems] = useState([]);
  const [itemCount, setItemCount] = useState();

  const cookItems = useCallback(items => {
    setItems(items);
    return items;
  }, []);

  const cookCount = useCallback(count => {
    setItemCount(count);
    return count;
  }, []);

  const [fetchAllRecords, setFetchAllRecords] = useState(false);
  const [downloadingPdf, setDownloadingPdf] = useState(false);
  const [allCurriculumStructure, setAllCurriculumStructure] = useState(null);

  const createAndSavePdf = useCallback(
    (head, body) => {
      const doc = new jsPDF();
      doc.setFontSize(PDF_TABLE_STYLES.DOC_FONT_SIZE);
      doc.text(
        t('Curriculum Structure'),
        PDF_TABLE_STYLES.HEADING_X,
        PDF_TABLE_STYLES.HEADING_Y,
      );
      autoTable(doc, {
        startY: PDF_TABLE_STYLES.TABLE_TOP_SPACE,
        head: [head],
        body,
        headStyles: {
          fillColor: PDF_TABLE_STYLES.HEAD_FILL_COLOR as [
            number,
            number,
            number,
          ],
          textColor: [0, 0, 0],
          fontStyle: 'normal',
        },
        styles: {
          cellPadding: { top: 3, bottom: 3, left: 2, right: 2 },
          valign: 'middle', // options: 'top' | 'middle' | 'bottom'
        },
      });
      doc.save('Curriculum Structure.pdf');
      setDownloadingPdf(false);
    },
    [t],
  );

  const computedTableData = useCallback(
    data => {
      const head = _columns.map(col => t(col.title as string));
      const body = data.map((item: ICurriculumStructureTable) =>
        _columns.map(col => {
          if (
            col.id === 'learningPlanCategoryName' &&
            item.learningPlanCategoryName !== item.learningPlanCategoryFullPath
          ) {
            const categories = item.learningPlanCategoryFullPath
              .split(' > ')
              .reverse();
            return categories;
          }
          return item[col.id];
        }),
      );
      return { head, body };
    },
    [_columns, t],
  );

  const onCompleted = useCallback(
    data => {
      if (data) {
        if (data.getCurriculumStructure) {
          setAllCurriculumStructure(data.getCurriculumStructure);
        }
        const { head, body } = computedTableData(
          data.getCurriculumStructure ? data.getCurriculumStructure : data,
        );
        createAndSavePdf(head, body);
      }
    },
    [createAndSavePdf, computedTableData],
  );

  const { loading } = useQuery(getCurriculumStructureGql, {
    variables: {
      programGroupId,
      first: 0,
      count: 100000,
      view: defaultFilterValues.view,
      programStructureLP:
        defaultFilterValues.programStructureLP
          ?.filter(({ id }) => typeof id !== 'string')
          // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
          // @ts-ignore
          .map(item => item.id) || [],
    },
    skip: !isNumber(programGroupId) || !fetchAllRecords,
    onCompleted,
  });

  const downloadAsPdf = useCallback(() => {
    setDownloadingPdf(true);
    if (items.length === itemCount) {
      onCompleted(items);
    } else if (!fetchAllRecords) {
      setFetchAllRecords(true);
    } else if (allCurriculumStructure) {
      onCompleted(allCurriculumStructure);
    }
    setDownloadingPdf(false);
  }, [items, itemCount, allCurriculumStructure, onCompleted, fetchAllRecords]);

  const renderFooter = useCallback(
    () => (
      <div className="pull-right">
        <RoundedPrimaryButton
          disabled={downloadingPdf || loading || !itemCount}
          onClick={downloadAsPdf}
        >
          {downloadingPdf || loading ? <Spinner /> : t('Download as PDF')}
        </RoundedPrimaryButton>
      </div>
    ),
    [t, downloadAsPdf, downloadingPdf, loading, itemCount],
  );

  return (
    <GqlFullCrudTable
      hasFilters
      hasPagination
      gql={{
        query: getCurriculumStructureGql,
        count: getCurriculumStructureCountGql,
        cookQueryVariables,
      }}
      isLoading={fLoading}
      list={{
        cookItems,
        cookCount,
        onFilterChange,
        filterComponent,
        onFilterChanged,
        initialFilter: {
          programGroupId,
          view: defaultFilterValues.view,
          programStructureLP: defaultFilterValues.programStructureLP,
          first: (Number(pageNumber) - 1) * pageSize,
          count: pageSize,
        },
        tableConfig: {
          columns: _columns,
        },
        customizableColumns: true,
        renderFooter,
      }}
      title={t('Curriculum Structure')}
    />
  );
}

export default memo(
  CurriculumStructureModuleView,
  (prevProps, nextProps) =>
    prevProps.programGroupTypeId === nextProps.programGroupTypeId &&
    prevProps.programGroupName === nextProps.programGroupName &&
    prevProps.fLoading === nextProps.fLoading &&
    prevProps.defaultFilterValues === nextProps.defaultFilterValues &&
    prevProps.onDefaultFilterValuesChange ===
      nextProps.onDefaultFilterValuesChange,
);
