import React from 'react';
import { includes } from 'lodash';
import {
  PROGRAM,
  SUBJECT,
  LEARNING_PLAN,
  LP_CATEGORY,
  LP_MODULE,
} from '../../../../../../../model/CurriculumStructureViewTypes';
import { ISimpleTableConfigColumn } from '../../../../../../../common/propTypes';
import ListToolTipCell from '../../../../../../EContent/EContentLibraries/EContentLibraryItems/common/ListTooltipCell';

export interface ICurriculumStructureTable {
  programId: number;
  programName: string;
  subjectId: number;
  subjectName: string;
  learningPlanId: number;
  learningPlanName: string;
  learningPlanCategoryId: number;
  learningPlanCategoryName: string;
  learningPlanCategoryFullPath: string;
  moduleId: number;
  moduleName: string;
}

export default function columns(
  programName,
  subjectName,
  learningPlanName,
  view,
) {
  const cols: ISimpleTableConfigColumn<ICurriculumStructureTable>[] = [];
  if (
    includes(
      [
        PROGRAM.value,
        SUBJECT.value,
        LEARNING_PLAN.value,
        LP_CATEGORY.value,
        LP_MODULE.value,
      ],
      view,
    )
  ) {
    cols.push({
      id: 'programName',
      title: programName,
      minWidth: 200,
      sortable: false,
      thClassName: 'pl-5',
      tdClassName: 'pl-5',
      cellRenderer: item => item.programName,
    });
  }
  if (
    includes(
      [SUBJECT.value, LEARNING_PLAN.value, LP_CATEGORY.value, LP_MODULE.value],
      view,
    )
  ) {
    cols.push({
      id: 'subjectName',
      title: subjectName,
      minWidth: 150,
      sortable: false,
      cellRenderer: item => item.subjectName,
    });
  }
  if (
    includes([LEARNING_PLAN.value, LP_CATEGORY.value, LP_MODULE.value], view)
  ) {
    cols.push({
      id: 'learningPlanName',
      title: learningPlanName,
      minWidth: 150,
      sortable: false,
      cellRenderer: item => item.learningPlanName,
    });
  }
  if (includes([LP_CATEGORY.value, LP_MODULE.value], view)) {
    cols.push({
      id: 'learningPlanCategoryName',
      title: 'Category',
      minWidth: 150,
      sortable: false,
      // eslint-disable-next-line react/display-name
      cellRenderer: item => {
        if (
          item.learningPlanCategoryName === item.learningPlanCategoryFullPath
        ) {
          return item.learningPlanCategoryName;
        }
        const categories = item.learningPlanCategoryFullPath
          .split(' > ')
          .reverse();
        // eslint-disable-next-line react/react-in-jsx-scope
        return <ListToolTipCell list={categories} />;
      },
    });
  }
  if (includes([LP_MODULE.value], view)) {
    cols.push({
      id: 'moduleName',
      title: 'Module',
      minWidth: 150,
      sortable: false,
      cellRenderer: item => item.moduleName,
    });
  }
  return cols;
}
