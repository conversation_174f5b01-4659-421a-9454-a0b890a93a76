query getCurriculumStructureContents(
  $ids: [Int]
  $searchQuery: String
  $first: Int
  $count: Int
) {
  getCurriculumStructureContents(
    ids: $ids
    searchQuery: $searchQuery
    first: $first
    count: $count
  ) {
    lessonGroupId
    lessonGroupName
    lessonId
    lessonName
    lessonStatus
    programId
    programName
    subjectId
    subjectName
    learningPlanId
    learningPlanName
    learningPlanCategoryId
    learningPlanCategoryName
    moduleId
    moduleName
    libraryId
  }
}
