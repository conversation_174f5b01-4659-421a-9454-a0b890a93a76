import React, { memo } from 'react';

import {
  CONTENT,
  LP_MODULE,
} from '../../../../../../model/CurriculumStructureViewTypes';
import useFilterStore from '../../../../../../common/components/controls/FilterBar/hooks/useFilterStore';
import CurriculumStructureModuleView from './CurriculumStructureModuleView';
import CurriculumStructureContentView from './CurriculumStructureContentView';

const FILTER_KEY = 'CURRICULUM_STRUCTURE_TABLE_FILTERS';
const WHITE = 255;
export const PDF_TABLE_STYLES = {
  HEAD_FILL_COLOR: [WHITE, WHITE, WHITE],
  DOC_FONT_SIZE: 12,
  PATH_FONT_SIZE: 11,
  HEADING_X: 14,
  HEADING_Y: 20,
  TABLE_TOP_SPACE: 28,
  TABLES_SPACE_BETWEEN: 10,
  LABEL_TABLE_TOP_SPACE: 6,
};

function CurriculumStructure({ programGroupTypeId, programGroupName }) {
  const {
    loading: fLoading,
    values: defaultFilterValues = {
      view: LP_MODULE.value,
      programStructureLP: [],
    },
    onChange: onDefaultFilterValuesChange,
  } = useFilterStore(FILTER_KEY, {
    defaultValues: {
      view: LP_MODULE.value,
      programStructureLP: [],
    },
  });

  if (defaultFilterValues.view === CONTENT.value) {
    return (
      <CurriculumStructureContentView
        defaultFilterValues={defaultFilterValues}
        programGroupName={programGroupName}
        onDefaultFilterValuesChange={onDefaultFilterValuesChange}
      />
    );
  }

  return (
    <CurriculumStructureModuleView
      defaultFilterValues={defaultFilterValues}
      fLoading={fLoading}
      programGroupName={programGroupName}
      programGroupTypeId={programGroupTypeId}
      onDefaultFilterValuesChange={onDefaultFilterValuesChange}
    />
  );
}

export default memo(
  CurriculumStructure,
  (prevProps, nextProps) =>
    prevProps.programGroupTypeId === nextProps.programGroupTypeId &&
    prevProps.programGroupName === nextProps.programGroupName,
);
