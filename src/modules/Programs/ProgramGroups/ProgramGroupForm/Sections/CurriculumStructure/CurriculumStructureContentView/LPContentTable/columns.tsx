export interface ICurriculumStructureContentsTable {
  lessonGroupId: number;
  lessonGroupName: string;
  lessonId: number;
  lessonName: string;
  lessonStatus: number;
  programId: number;
  programName: string;
  subjectId: number;
  subjectName: string;
  learningPlanId: number;
  learningPlanName: string;
  learningPlanCategoryId: string;
  learningPlanCategoryName: string;
  moduleId: number;
  moduleName: string;
  libraryId: number;
}

export default function columns() {
  return [
    {
      id: 'lessonGroupName',
      title: 'Lesson Group',
      minWidth: 200,
      sortable: false,
      thClassName: 'pl-5',
      tdClassName: 'pl-5',
      cellRenderer: item => item.lessonGroupName,
    },
    {
      id: 'lessonName',
      title: 'Lesson Name',
      minWidth: 200,
      sortable: false,
      cellRenderer: item => item.lessonName,
    },
  ];
}
