import React, { FC, useCallback, useMemo, useState } from 'react';
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';
import { isNumber } from 'lodash';
import { useQuery } from 'react-apollo';

import { useTranslation } from '../../../../../../../../common/components/utils/Translations';
import getCurriculumStructureContentsGql from '../../data/getCurriculumStructureContents.graphql';
import getCurriculumStructureContentsCountGql from '../../data/getCurriculumStructureContentsCount.graphql';
import GqlFullCrudTable from '../../../../../../../../common/components/dataViews/GqlFullCrudTable';
import usePaginationRouting from '../../../../../../../../common/data/hooks/usePaginationRouting';
import ILearningPlanTask from '../../../../../../../../common/abstract/OrganisationGroup/ProgramGroups/ILearningPlanTask';
import columns, { ICurriculumStructureContentsTable } from './columns';
import { redirectNewTab } from '../../../../../../../../common/utils/urlHelper';
import Spinner from '../../../../../../../../common/components/utils/Spinner';
import { ICurriculumStructureTable } from '../../CurriculumStructureModuleView/columns';
import { PDF_TABLE_STYLES } from '../../CurriculumStructure';
import RoundedDropdownButton from '../../../../../../../../common/components/controls/RoundedDropdownButton';

const LPContentTable: FC<{
  node: ILearningPlanTask;
  programStructureLPIds: number[];
}> = ({ node, programStructureLPIds }) => {
  const { t } = useTranslation();
  const { pageNumber, pageSize } = usePaginationRouting();

  const _columns = useMemo(() => columns(), []);

  const wrapperComponent = useCallback(
    ({ children }) => <div>{children}</div>,
    [],
  );

  const openItem = useCallback((row: ICurriculumStructureContentsTable) => {
    const link = `/e-content/libraries/edit/${row.libraryId}/edit/${row.lessonGroupId}/contents/edit/${row.lessonId}/contents`;
    redirectNewTab(link);
  }, []);

  const handleRowClick = useCallback(
    model => {
      openItem(model);
    },
    [openItem],
  );

  const [items, setItems] = useState([]);
  const [itemCount, setItemCount] = useState();

  const [fetchAllRecords, setFetchAllRecords] = useState(false);
  const [downloadingPdf, setDownloadingPdf] = useState(false);
  const [
    allCurriculumStructureContents,
    setAllCurriculumStructureContents,
  ] = useState(null);

  const createAndSavePdf = useCallback(
    (head, body) => {
      const doc = new jsPDF();
      doc.setFontSize(PDF_TABLE_STYLES.DOC_FONT_SIZE);
      doc.text(
        t('Curriculum Structure'),
        PDF_TABLE_STYLES.HEADING_X,
        PDF_TABLE_STYLES.HEADING_Y,
      );
      autoTable(doc, {
        startY: PDF_TABLE_STYLES.TABLE_TOP_SPACE,
        head: [head],
        body,
        headStyles: {
          fillColor: PDF_TABLE_STYLES.HEAD_FILL_COLOR as [
            number,
            number,
            number,
          ],
          textColor: [0, 0, 0],
          fontStyle: 'normal',
        },
        styles: {
          cellPadding: { top: 3, bottom: 3, left: 2, right: 2 },
          valign: 'middle',
        },
      });
      doc.save('Curriculum Structure.pdf');
      setDownloadingPdf(false);
    },
    [t],
  );

  const createAndSavePdfByModule = useCallback(
    (head, bodies) => {
      const doc = new jsPDF();
      doc.setFontSize(PDF_TABLE_STYLES.DOC_FONT_SIZE);

      // Initial heading
      doc.text(
        t('Curriculum Structure'),
        PDF_TABLE_STYLES.HEADING_X,
        PDF_TABLE_STYLES.HEADING_Y,
      );

      let currentY =
        PDF_TABLE_STYLES.HEADING_Y + PDF_TABLE_STYLES.TABLES_SPACE_BETWEEN; // Add some space after heading

      doc.setFontSize(PDF_TABLE_STYLES.PATH_FONT_SIZE);
      for (const i of bodies) {
        // Add module path before the table
        doc.text(i.path, PDF_TABLE_STYLES.HEADING_X, currentY);
        currentY += PDF_TABLE_STYLES.LABEL_TABLE_TOP_SPACE; // Add spacing between the label and table

        autoTable(doc, {
          startY: currentY,
          head: [head],
          body: i.body,
          headStyles: {
            fillColor: PDF_TABLE_STYLES.HEAD_FILL_COLOR as [
              number,
              number,
              number,
            ],
            textColor: [0, 0, 0],
            fontStyle: 'normal',
          },
          styles: {
            cellPadding: { top: 3, bottom: 3, left: 2, right: 2 },
            valign: 'middle',
          },
        });

        // Update currentY to the end of this table
        currentY =
          (doc as any).lastAutoTable.finalY +
          PDF_TABLE_STYLES.TABLES_SPACE_BETWEEN; // add spacing between tables
      }

      doc.save('Curriculum Structure.pdf');
      setDownloadingPdf(false);
    },
    [t],
  );

  const computedTableData = useCallback(
    data => {
      const head = _columns.map(col => t(col.title as string));
      const body = data.map((item: ICurriculumStructureTable) =>
        _columns.map(col => item[col.id]),
      );
      return { head, body };
    },
    [_columns, t],
  );

  const computedTableDataByModule = useCallback(
    data => {
      const head = _columns.map(col => t(col.title as string));

      const groupedByModule = data.reduce((acc, item) => {
        const key = item.moduleId;
        if (!acc[key]) {
          acc[key] = [];
        }
        acc[key].push(item);
        return acc;
      }, {});

      const bodies = Object.entries(groupedByModule).map(([key, items]) => {
        const body = (items as ICurriculumStructureTable[]).map(
          (item: ICurriculumStructureTable) =>
            _columns.map(col => item[col.id]),
        );
        const path = `${
          (items as ICurriculumStructureTable[])[0].learningPlanCategoryName
        } > ${(items as ICurriculumStructureTable[])[0].moduleName}`;
        return {
          key,
          path,
          body,
        };
      });

      return { head, bodies };
    },
    [_columns, t],
  );

  const onCompleted = useCallback(
    data => {
      if (data) {
        if (data.getCurriculumStructureContents) {
          setAllCurriculumStructureContents(
            data.getCurriculumStructureContents,
          );
        }
        const { head, bodies } = computedTableDataByModule(
          data.getCurriculumStructureContents
            ? data.getCurriculumStructureContents
            : data,
        );
        createAndSavePdfByModule(head, bodies);
      }
    },
    [createAndSavePdfByModule, computedTableDataByModule],
  );

  const { loading } = useQuery(getCurriculumStructureContentsGql, {
    variables: {
      ids: programStructureLPIds,
      first: 0,
      count: 10000,
      searchQuery: '',
    },
    skip: !isNumber(node.id) || !fetchAllRecords,
    onCompleted,
  });

  const downloadCurrentViewAsPdf = useCallback(() => {
    setDownloadingPdf(true);
    const { head, body } = computedTableData(items);
    createAndSavePdf(head, body);
    setDownloadingPdf(false);
  }, [items, createAndSavePdf, computedTableData]);

  const downloadAllAsPdf = useCallback(() => {
    setDownloadingPdf(true);
    if (!fetchAllRecords) {
      setFetchAllRecords(true);
    } else if (allCurriculumStructureContents) {
      onCompleted(allCurriculumStructureContents);
    }
    setDownloadingPdf(false);
  }, [allCurriculumStructureContents, onCompleted, fetchAllRecords]);

  const cookItems = useCallback(items => {
    setItems(items);
    return items;
  }, []);

  const cookCount = useCallback(count => {
    setItemCount(count);
    return count;
  }, []);

  const renderFooter = useCallback(() => {
    const actions = [
      {
        text: t('Current View'),
        onClick: downloadCurrentViewAsPdf,
      },
      {
        text: t('Full Report'),
        onClick: downloadAllAsPdf,
      },
    ];
    return (
      <div className="pull-right">
        <RoundedDropdownButton
          clickableBtn
          isSubmitTogglesDropdown
          actions={actions}
          disabled={downloadingPdf || loading || !itemCount}
          // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
          // @ts-ignore
          type="button"
        >
          {downloadingPdf || loading ? <Spinner /> : t('Download as PDF')}
        </RoundedDropdownButton>
      </div>
    );
  }, [
    t,
    downloadAllAsPdf,
    downloadCurrentViewAsPdf,
    downloadingPdf,
    loading,
    itemCount,
  ]);

  return (
    <GqlFullCrudTable
      hasPagination
      gql={{
        query: getCurriculumStructureContentsGql,
        count: getCurriculumStructureContentsCountGql,
      }}
      list={{
        cookItems,
        cookCount,
        onRowClick: handleRowClick,
        hasSearchFieldOnly: true,
        initialFilter: {
          ids: [node.id],
          first: (Number(pageNumber) - 1) * pageSize,
          count: pageSize,
        },
        wrapper: wrapperComponent,
        tableConfig: {
          columns: _columns,
        },
        renderFooter,
      }}
      title={t('Curriculum Structure')}
    />
  );
};

export default LPContentTable;
