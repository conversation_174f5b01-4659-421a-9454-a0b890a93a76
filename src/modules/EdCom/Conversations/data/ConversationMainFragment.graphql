#import '../../../../common/data/fragments/FileAttachmentMainFragment.graphql'

fragment ConversationMain on Conversation {
    id
    personEntityAllocationId
    organisationId
    topic
    status
    conversationArea
    moduleIcon
    dateFrom
    dateTo
    createdAt
    updatedAt
    ownerPersonId
    typePartCount

    logoAttachment {
        ...FileAttachmentMain
    }

    createdByPerson {
        id
        firstName
        lastName
        gender
        isOnline
        organisationGroupId
        tenantId
        photoFileId
    }

    participantsCount
    unreadConversationItemCount
    unreadConversationNotesCount

    organisationAllocationNames {
        organisationName
    }
}
