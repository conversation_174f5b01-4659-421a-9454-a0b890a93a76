import { keyBy } from 'lodash';
import TConversationStatus from '../abstract/TConversationStatus';
import { TBasicProp } from '../../../../common/data/hooks/useEnumerableModel';

interface IConversationStatusItem extends TBasicProp {
  value: TConversationStatus;
  name: string;
  sequence: number;
}

export const ACTIVE: IConversationStatusItem = {
  value: 'ACTIVE',
  name: 'Active',
  sequence: 1,
};
export const HIDDEN: IConversationStatusItem = {
  value: 'HIDDEN',
  name: 'Hidden',
  sequence: 2,
};
export const READ_ONLY: IConversationStatusItem = {
  value: 'READ_ONLY',
  name: 'Read-only',
  sequence: 3,
};
export const DELETED: IConversationStatusItem = {
  value: 'DELETED',
  name: 'Deleted',
  sequence: 4,
};

const Basic: IConversationStatusItem[] = [ACTIVE, HIDDEN, READ_ONLY, DELETED];
const BasicByValue = keyBy(Basic, 'value');
const BasicDefault = [ACTIVE, HIDDEN, READ_ONLY, DELETED].map(
  ({ value }) => value,
);
const BasicProps = Object.keys(BasicByValue);

export default {
  Basic,
  BasicDefault,
  BasicByValue,
  BasicProps,
};
