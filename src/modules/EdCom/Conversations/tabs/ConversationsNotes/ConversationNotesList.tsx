import React, { FC, useCallback, useMemo, useState } from 'react';
import { map } from 'lodash';

import useEdComSelectedItem from '../../../EdComCrud/context/EdComSelectedItem';
import IConversation from '../../abstract/IConversation';
import IConversationNote from './abstract/IConversationNote';
import ThreadMessageForm from '../ConversationsTab/ThreadMessageForm';
import { InfinityScrollConnection } from '../../../../../common/utils/infinityScroll';
import query from '../../data/conversationNotesConnection.graphql';
import useThreadMessagesList from '../ConversationsTab/hook/useThreadMessagesList';
import ConversationNote from './ConversationNote';
import styles from './ConversationNotesList.scss';

const ConversationNotesList: FC = () => {
  const { selectedItem } = useEdComSelectedItem<IConversation>();
  const variables = useMemo(() => ({ conversationId: selectedItem?.id }), [
    selectedItem,
  ]);

  const [isEditMode, setIsEditMode] = useState(false);
  const { items, setItems } = useThreadMessagesList<IConversationNote>();
  const handleNewDataLoaded = useCallback(
    _items => {
      setItems(items => [..._items, ...items]);
    },
    [setItems],
  );

  const renderItems = useCallback(
    items =>
      map(items, item => (
        <ConversationNote
          key={item.id}
          isEditMode={isEditMode}
          message={item}
          setIsEditMode={setIsEditMode}
        />
      )),
    [isEditMode, setIsEditMode],
  );

  return (
    <div className={styles.main}>
      <ThreadMessageForm
        isNotes
        isEditMode={isEditMode}
        setIsEditMode={setIsEditMode}
      >
        <InfinityScrollConnection<IConversationNote>
          hasEmptyPlaceholder
          gqlConnectionQuery={query}
          gqlConnectionVariables={variables}
          height={800}
          items={items}
          mode="DESC"
          renderItems={renderItems}
          onNewDataLoaded={handleNewDataLoaded}
        />
      </ThreadMessageForm>
    </div>
  );
};

export default ConversationNotesList;
