import React, { FC } from 'react';

import ConversationStatus from '../../model/ConversationStatus';
import SelectBoxField from '../../../../../common/components/containers/EntityForm/fields/SelectBoxField';
import useT from '../../../../../common/components/utils/Translations/useT';
import useEnumerableModel from '../../../../../common/data/hooks/useEnumerableModel';

interface IConversationStatusField {
  isDisabled?: boolean;
}

const ConversationStatusField: FC<IConversationStatusField> = ({
  isDisabled,
}) => {
  const t = useT();
  const { options } = useEnumerableModel(ConversationStatus);

  return (
    <SelectBoxField
      required
      disabled={isDisabled}
      label={t('Status')}
      name="status"
      options={options}
    />
  );
};
export default ConversationStatusField;
