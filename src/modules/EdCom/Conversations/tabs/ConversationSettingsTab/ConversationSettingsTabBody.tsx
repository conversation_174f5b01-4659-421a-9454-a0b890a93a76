import React, { FC, useEffect } from 'react';
import classNames from 'classnames';
import { isEmpty } from 'lodash';

import ConversationStatusField from './ConversationStatusField';
import { ED_COM_CONVERSATION_ATTACHMENTS } from '../../../../../fsCategories';
import AttachFileField from '../../../../../common/components/containers/EntityForm/fields/AttachFileField';
import EntityNameField from '../../../../../common/components/containers/EntityForm/fields/EntityNameField';

import DateRangeField from '../../../../../common/components/containers/EntityForm/fields/DateRangeField';
import useT from '../../../../../common/components/utils/Translations/useT';
import EdComOrganisationSelectorField, {
  COMBINED_ID,
} from '../../../common/EdComOrganisationSelectorField';
import { IBasicEntity } from '../../../../../common/components/containers/EntityForm/EntityForm';
import { IFileAttachmentTemp } from '../../../../../common/abstract/IFileAttachments';
import TConversationStatus from '../../abstract/TConversationStatus';
import useEdComTempItem from '../../../EdComCrud/context/EdComTempItem';
import IConversationTemp from '../../abstract/IConversationTemp';
import EntityFormFieldSet from '../../../../../common/components/containers/EntityForm/EntityFormFieldSet';
import style from './ConversationSettingsTab.scss';
import TextField from '../../../../../common/components/containers/EntityForm/fields/TextField';
import useEntityFormContext from '../../../../../common/components/containers/EntityForm/internal/useEntityFormContext';
import useEdComSelectedItem from '../../../EdComCrud/context/EdComSelectedItem';
import IConversation from '../../abstract/IConversation';
import { ONE_TO_ONE } from '../../model/ConversationTypePartCount';
import { STAFF } from '../../../../../model/PersonEntityType';
import { useMobileDetection } from '../../../../EContent/EContentLibraries/EContentLibraryItems/form/tabs/EContentItemContentsTab/form/Question/QuestionSetOption';

const ConversationSettingsTabBody: FC = () => {
  const isMobile = useMobileDetection();

  const { tempData } = useEdComTempItem<IConversationTemp>();
  const { selectedItem } = useEdComSelectedItem<IConversation>();
  const t = useT();

  const isOneToOne = selectedItem?.typePartCount === ONE_TO_ONE.value;

  const { setFieldValue } = useEntityFormContext();

  useEffect(() => {
    if (!isEmpty(tempData.participantsRemoved)) {
      setFieldValue('participantsRemoved', tempData.participantsRemoved);
    }

    if (!isEmpty(tempData.participantsAdded)) {
      setFieldValue('participantsAdded', tempData.participantsAdded);
    }
  }, []);

  const organisationName =
    selectedItem?.organisationAllocationNames?.organisationName;

  return (
    <>
      <EntityFormFieldSet>
        <AttachFileField
          columns={isMobile ? 0 : -1}
          fileCategory={ED_COM_CONVERSATION_ATTACHMENTS}
          name="logoAttachment"
        />
        <EntityNameField
          disabled={isOneToOne}
          label={t('Topic')}
          name="topic"
        />
        <ConversationStatusField isDisabled={isOneToOne} />
      </EntityFormFieldSet>

      <EntityFormFieldSet>
        <div
          className={classNames(
            'inline pull-left visible-lg-block',
            style.dummyPlaceHolder,
          )}
        />
        <TextField
          isReadOnly
          label={t('Conversation Area')}
          name="conversationArea"
        />
        <DateRangeField
          hasTimePicker
          endDateName="dateTo"
          label={t('Active Duration')}
          startDateName="dateFrom"
        />
      </EntityFormFieldSet>

      <EntityFormFieldSet>
        <div
          className={classNames(
            'inline pull-left visible-lg-block',
            style.dummyPlaceHolder,
          )}
        />
        <EdComOrganisationSelectorField
          hasStructureLevel={false}
          isDisabled={!!selectedItem?.id}
          name="personEntityAllocationId"
          orgName={organisationName}
        />
      </EntityFormFieldSet>
    </>
  );
};

interface IConversationSettingsTabEntity extends IBasicEntity {
  topic: string;
  status: TConversationStatus;
  dateFrom?: Date;
  dateTo?: Date;
  organisationId?: number;
  personEntityAllocationId?: number;
  orgStructureLevelId?: number;
  logoAttachment?: IFileAttachmentTemp;
  conversationArea?: string;
}

export default ConversationSettingsTabBody;
