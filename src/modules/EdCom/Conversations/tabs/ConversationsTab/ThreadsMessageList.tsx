import React, { RefObject } from 'react';
import { map } from 'lodash';

import ThreadMessage from './ThreadMessage/ThreadMessage';

import IThreadMessageItem from './abstract/IThreadMessageItem';
import { TextEditorRef } from '../../../../../common/components/controls/base/TextEditor/TextEditor';

interface IThreadsMessageList<T extends IThreadMessageItem> {
  items: T[];
  ownerPersonId: string;
  mainTextEditorRef?: RefObject<TextEditorRef>;
  isEditMode?: boolean;
  setIsEditMode?: React.Dispatch<React.SetStateAction<boolean>>;
  editingMessageId?: number | null;
  setEditingMessageId?: React.Dispatch<React.SetStateAction<number | null>>;
}

function ThreadsMessageList<T extends IThreadMessageItem>({
  items,
  ownerPersonId,
  mainTextEditorRef,
  isEditMode,
  setIsEditMode,
  editingMessageId,
  setEditingMessageId,
}: IThreadsMessageList<T>) {
  return (
    <>
      {map(items, item => (
        <ThreadMessage
          key={item.id}
          isCore
          editingMessageId={editingMessageId}
          isEditMode={isEditMode}
          mainTextEditorRef={mainTextEditorRef}
          message={item}
          ownerPersonId={ownerPersonId}
          setEditingMessageId={setEditingMessageId}
          setIsEditMode={setIsEditMode}
        />
      ))}
    </>
  );
}

export default ThreadsMessageList;
