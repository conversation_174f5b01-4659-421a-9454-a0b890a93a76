import React, { useCallback, useRef, useState } from 'react';

import { InfinityScrollConnection } from '../../../../../common/utils/infinityScroll';
import ThreadsMessageList from './ThreadsMessageList';

import IThreadMessageItem from './abstract/IThreadMessageItem';

import style from './ThreadMessageListControl.scss';
import useThreadMessagesList from './hook/useThreadMessagesList';
import useThreadMessagesQuery from './hook/useThreadMessagesQuery';
import ThreadMessageForm from './ThreadMessageForm';
import ThreadMessageReadedProvider from './context/ThreadMessageReadedProvider';
import useAnchor from './hook/useAnchor';
import useSocketHandler from '../../../../../common/data/socket/useSocketHandler';
import useCurrentUser from '../../../../../common/data/hooks/useCurrentUser';
import ConversationMessageNotificationType from '../../model/ConversationMessageNotificationType';
import { TextEditorRef } from '../../../../../common/components/controls/base/TextEditor/TextEditor';

export default function ThreadsMessageListControl({
  conversationId,
  ownerPersonId,
}: {
  conversationId: number;
  ownerPersonId: string;
}) {
  const mainTextEditorRef = useRef<TextEditorRef>(null);
  const {
    currentUser: { id: currentUserId },
  } = useCurrentUser();
  const [isEditMode, setIsEditMode] = useState(false);
  const [editingMessageId, setEditingMessageId] = useState<number | null>(null);
  const { query, variables } = useThreadMessagesQuery();
  const {
    items,
    setItemsWithUnreadCheck,
    deleteItem,
    applyReaction,
    replaceItem,
  } = useThreadMessagesList();

  const { anchorParentId, anchorId } = useAnchor();

  const anchor = anchorParentId || anchorId || undefined;

  const handleNewDataLoaded = useCallback(
    (_items: IThreadMessageItem[]) => {
      setItemsWithUnreadCheck([..._items]);
    },
    [setItemsWithUnreadCheck],
  );

  const handleIncomingRealtimeMessage = useCallback(
    message => {
      if (
        ConversationMessageNotificationType.isCreated(message.notificationType)
      ) {
        const { parentId, id } = message;

        if (parentId) {
          const updatedMessage = items.find(x => x.id === parentId);

          if (!updatedMessage) {
            return;
          }

          updatedMessage.threadSize++;
          updatedMessage.unreadCount++;

          return replaceItem({ ...updatedMessage });
        }

        if (items.some(x => x.id === id)) {
          return;
        }

        return setItemsWithUnreadCheck(
          [
            {
              ...message,
              isStarred: false,
              isLiked: false,
              likeCount: 0,
              threadSize: 0,
              unreadCount: 0,
            },
          ],
          'DESC',
        );
      }

      if (
        ConversationMessageNotificationType.isDeleted(message.notificationType)
      ) {
        return deleteItem(message.conversationItemId);
      }

      if (
        ConversationMessageNotificationType.isReacted(message.notificationType)
      ) {
        return applyReaction(message.conversationItemId, {
          reactionType: message.reactionType,
          isCurrentUserReaction: message.participantPersonId === currentUserId,
        });
      }

      if (
        ConversationMessageNotificationType.isUpdated(message.notificationType)
      ) {
        const { parentId, id, content, updatedAt } = message;

        const currentMessage = items.find(x => x.id === id);

        if (parentId || !currentMessage) {
          return;
        }

        return replaceItem({ ...currentMessage, content, updatedAt });
      }
    },
    [
      applyReaction,
      replaceItem,
      deleteItem,
      setItemsWithUnreadCheck,
      currentUserId,
      items,
    ],
  );

  useSocketHandler(
    `conversation/${conversationId}`,
    handleIncomingRealtimeMessage,
  );

  const renderItems = useCallback(
    (items: IThreadMessageItem[]) => (
      <ThreadsMessageList<IThreadMessageItem>
        editingMessageId={editingMessageId}
        isEditMode={isEditMode}
        items={items}
        mainTextEditorRef={mainTextEditorRef}
        ownerPersonId={ownerPersonId}
        setEditingMessageId={setEditingMessageId}
        setIsEditMode={setIsEditMode}
      />
    ),
    [
      mainTextEditorRef,
      isEditMode,
      editingMessageId,
      setEditingMessageId,
      ownerPersonId,
    ],
  );
  return (
    <div className={style.main}>
      <ThreadMessageReadedProvider>
        <ThreadMessageForm
          ref={mainTextEditorRef}
          isEditMode={isEditMode}
          setIsEditMode={setIsEditMode}
        >
          <InfinityScrollConnection<IThreadMessageItem>
            hasEmptyPlaceholder
            anchor={anchor}
            gqlConnectionQuery={query}
            gqlConnectionVariables={variables}
            height={800}
            items={items}
            mode="DESC"
            renderItems={renderItems}
            onNewDataLoaded={handleNewDataLoaded}
          />
        </ThreadMessageForm>
      </ThreadMessageReadedProvider>
    </div>
  );
}
