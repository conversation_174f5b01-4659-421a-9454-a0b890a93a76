import React, { useMemo } from 'react';
import classNames from 'classnames';
import IconButton from '../../../../../common/components/controls/IconButton';
import useT from '../../../../../common/components/utils/Translations/useT';
import toEdanaTimestamp9 from '../../../../../common/utils/edanaTimestamp9';
import { IThreadMessageItemQuote } from './abstract/IThreadMessageItem';
import styles from './QuoteMessage.scss';

interface IQuoteMessage {
  message?: IThreadMessageItemQuote;
  onCancel?: () => void;
  onClick?: () => void;
}

export default function QuoteMessage({
  message,
  onCancel,
  onClick,
}: IQuoteMessage) {
  const markup = useMemo(() => {
    if (!message?.content) return { __html: '' };

    const wrapper = document.createElement('div');
    wrapper.innerHTML = message.content;

    wrapper.childNodes.forEach(node => {
      if (node.nodeType === Node.ELEMENT_NODE) {
        // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
        // @ts-ignore
        node.innerHTML = `"${node.innerHTML}"`;
      }
    });

    return { __html: wrapper.innerHTML };
  }, [message]);

  const t = useT();
  if (!message) {
    return <></>;
  }

  return (
    <div
      className={classNames(styles.container, 'mt-10', 'p-5', 'bg-btn', {
        [styles.clickable]: onClick,
      })}
      onClick={onClick}
    >
      <div className="pl-5 w-100 border-left-lg border-grey">
        {/*eslint-disable-next-line react/no-danger*/}
        <div dangerouslySetInnerHTML={markup} />
        <span>
          {`${message.person.fullName}, ${toEdanaTimestamp9(
            t,
            message.createdAt,
          )}`}
        </span>
        <br />
      </div>
      {onCancel && (
        <IconButton
          isSmall
          className={styles.removeIcon}
          hasBackground={false}
          iconName="cross"
          onClick={onCancel}
        />
      )}
    </div>
  );
}
