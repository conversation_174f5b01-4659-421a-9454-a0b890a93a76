import React, { FC, useMemo } from 'react';

import ThreadsMessageListControl from './ThreadsMessageListControl';
import useEdComSelectedItem from '../../../EdComCrud/context/EdComSelectedItem';
import IConversation from '../../abstract/IConversation';

import conversationItemsConnectionGql from '../../data/conversationItemsConnection.graphql';
import reactConversationItemGql from '../../data/reactConversationItem.graphql';
import updateConversationItemGql from '../../data/updateConversationItem.graphql';
import createConversationItemGql from '../../data/createConversationItem.graphql';
import deleteConversationItemGql from '../../data/deleteConversationItem.graphql';
import reportConversationItemGql from '../../data/reportConversationItem.graphql';
import readConversationItemGql from '../../data/readConversationItem.graphql';
import conversationItemGql from '../../data/conversationItem.graphql';
import conversationsItemsUnreadCountQuery from '../../data/conversationsItemsUnreadCount.graphql';
import conversationQuery from '../../data/conversation.graphql';

import { DefaultPageSize } from '../../../../../common/components/other/PageSizeSelect';
import ThreadMessagesQueryContext from './context/ThreadMessagesQueryContext';

import ThreadMessageActionContext, {
  IThreadMessageActionContext,
} from './context/ThreadMessageActionContext';

import ThreadMessagesProvider from './context/ThreadMessagesProvider';

const ConversationsTab: FC = () => {
  const { selectedItem } = useEdComSelectedItem<IConversation>();
  const conversationId = selectedItem?.id as number;
  const ownerPersonId = selectedItem?.ownerPersonId as string;

  const variables = useMemo(
    () => ({
      conversationId,
      parentId: null,
      count: DefaultPageSize,
      after: null,
    }),
    [conversationId],
  );

  const actionContext = useMemo<IThreadMessageActionContext>(
    () => ({
      likeQuery: reactConversationItemGql,
      starQuery: reactConversationItemGql,
      createQuery: createConversationItemGql,
      updateQuery: updateConversationItemGql,
      reportQuery: reportConversationItemGql,
      deleteQuery: deleteConversationItemGql,
      singleQuery: conversationItemGql,
      readQuery: readConversationItemGql,
      readQueryResolver: options => ({
        ...options,
        refetchQueries: [
          { query: conversationsItemsUnreadCountQuery },
          { query: conversationQuery, variables: { id: conversationId } },
        ],
      }),
    }),
    [conversationId],
  );

  const queryContext = useMemo(
    () => ({ query: conversationItemsConnectionGql, variables }),
    [variables],
  );

  return (
    <ThreadMessagesProvider>
      <ThreadMessagesQueryContext.Provider value={queryContext}>
        <ThreadMessageActionContext.Provider value={actionContext}>
          <ThreadsMessageListControl
            conversationId={conversationId}
            ownerPersonId={ownerPersonId}
          />
        </ThreadMessageActionContext.Provider>
      </ThreadMessagesQueryContext.Provider>
    </ThreadMessagesProvider>
  );
};

export default ConversationsTab;
