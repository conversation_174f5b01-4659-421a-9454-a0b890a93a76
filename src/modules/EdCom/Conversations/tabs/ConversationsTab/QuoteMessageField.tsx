import React, { useCallback } from 'react';
import EntityField from '../../../../../common/components/containers/EntityForm/internal/EntityField';
import QuoteMessage from './QuoteMessage';
import useEntityFormContext from '../../../../../common/components/containers/EntityForm/internal/useEntityFormContext';
import IThreadMessageItem from './abstract/IThreadMessageItem';

interface IQuoteMessageField {
  name?: string;
}

export default function QuoteMessageField({
  name = 'threadQuote',
}: IQuoteMessageField) {
  const { setFieldValue } = useEntityFormContext();
  const handleCancel = useCallback(() => {
    setFieldValue(name, null);
  }, [name, setFieldValue]);

  return (
    <EntityField<IThreadMessageItem> columns={1} name={name}>
      {(editing, field) => (
        <QuoteMessage message={field.input.value} onCancel={handleCancel} />
      )}
    </EntityField>
  );
}
