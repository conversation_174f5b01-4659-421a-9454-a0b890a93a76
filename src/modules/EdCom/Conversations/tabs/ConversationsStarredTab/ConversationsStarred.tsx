import React, { FC, useCallback, useMemo } from 'react';
import { useMutation } from 'react-apollo';
import copy from 'copy-to-clipboard';
import { useHistory } from 'react-router-dom';
import classNames from 'classnames';

import useT from '../../../../../common/components/utils/Translations/useT';
import useThreadMessagesList from '../ConversationsTab/hook/useThreadMessagesList';
import useThreadMessageAction from '../ConversationsTab/hook/useThreadMessageAction';
import toEdanaTimestamp4 from '../../../../../common/utils/edanaTimestamp4';
import { IDropdownAction } from '../../../../../common/components/controls/Dropdown';

import style from '../ConversationsTab/ThreadMessage/ThreadMessage.scss';
import PersonAvatar from '../../../../../common/components/utils/PersonAvatar';
import AttachmentList from '../../../../../common/components/controls/Attachments/lists/AttachmentList';
import Tooltip from '../../../../../common/components/utils/Tooltip';
import Icon from '../../../../../common/components/utils/Icon';
import toEdanaTimestamp1 from '../../../../../common/utils/edanaTimestamp1';
import IThreadMessageItem from '../ConversationsTab/abstract/IThreadMessageItem';
import ThreadMessageActionSection from '../ConversationsTab/ThreadMessage/ThreadMessageActionSection';
import SpinnerError from '../../../../../common/components/utils/SpinnerError';
import { dummyMutation } from '../../../../../common/data/dummyMutation';

import {
  LIKE,
  STAR,
  UNLIKE,
  UNSTAR,
} from '../ConversationsTab/model/ReactionTypes';
import getGqlOperationName from '../../../../../common/utils/getGqlOperationName';
import useEdComTabCoreUrls from '../../../EdComCrud/context/EdComTabCoreUrls';
import useIsConversationOpen from '../../hooks/useIsConversationOpen';
import stripHtml from '../../../../../common/utils/stripHtml';

interface IConversationStarredProps {
  message: IThreadMessageItem;
}

const ConversationStarred: FC<IConversationStarredProps> = ({ message }) => {
  const {
    person,
    content,
    createdAt,
    updatedAt,
    likeCount,
    isStarred,
    isLiked,
    attachments,
    id,
    parentId,
  } = message;

  const { push } = useHistory();

  const { likeQuery, starQuery } = useThreadMessageAction();
  const { replaceItem, deleteItem } = useThreadMessagesList();
  const { tabsRootUrl } = useEdComTabCoreUrls();

  const t = useT();

  const [onLike, { error: likeError, loading: likeLoading }] = useMutation(
    likeQuery || dummyMutation,
  );
  const [onStar, { error: starError, loading: starLoading }] = useMutation(
    starQuery || dummyMutation,
  );

  const actions = useMemo(() => {
    const actions: IDropdownAction[] = [
      {
        label: t('Copy'),
        onClick: () => copy(stripHtml(content)),
      },
      {
        label: t('Go to Conversation Tab'),
        onClick: () => {
          push(
            `${tabsRootUrl}/conversations/#${
              parentId ? `${id}_${parentId}` : `${id}`
            }`,
          );
        },
      },
    ];

    return actions;
  }, [t, content, tabsRootUrl, parentId, id, push]);

  const _onLike = useCallback(async () => {
    const { data } = await onLike({
      variables: {
        params: {
          conversationItemId: message.id,
          reactionType: message.isLiked ? UNLIKE : LIKE,
        },
      },
    });

    replaceItem(data[getGqlOperationName(likeQuery)]);
  }, [onLike, likeQuery, replaceItem, message.id, message.isLiked]);

  const _onStar = useCallback(async () => {
    const { data } = await onStar({
      variables: {
        params: {
          conversationItemId: message.id,
          reactionType: message.isStarred ? UNSTAR : STAR,
        },
      },
    });
    deleteItem(data[getGqlOperationName(starQuery)].id);
  }, [onStar, deleteItem, starQuery, message.id, message.isStarred]);

  const markup = useMemo(() => ({ __html: content }), [content]);

  const renderTooltip = useCallback(
    () => (
      <>
        <p>{toEdanaTimestamp4(createdAt)}</p>
        <p>
          {t('Edited: ')}
          {toEdanaTimestamp4(updatedAt)}
        </p>
      </>
    ),
    [createdAt, updatedAt, t],
  );

  const error = likeError || starError;

  const isConversationOpen = useIsConversationOpen();

  return (
    <SpinnerError error={error}>
      <div className={classNames(style.main, style.threadBorder)}>
        <div className={style.contentContainer}>
          <PersonAvatar person={person} size="xs" />
          <div>
            <b>{person.fullName}</b>
            <div className={style.content}>
              {/*eslint-disable-next-line react/no-danger*/}
              <div dangerouslySetInnerHTML={markup} />
              {attachments?.length > 0 && (
                <AttachmentList
                  isDownloadable
                  attachments={attachments}
                  editable={false}
                />
              )}
            </div>
          </div>
        </div>

        <div className={style.rightPart}>
          <p className={style.tooltip}>
            {updatedAt !== createdAt && (
              <Tooltip position="responsive" render={renderTooltip}>
                <Icon name="pencil" />
              </Tooltip>
            )}
            {toEdanaTimestamp1(t, createdAt)}
          </p>
          <ThreadMessageActionSection
            actions={actions}
            isLiked={isLiked}
            isLikeDisabled={likeLoading || !isConversationOpen}
            isStarrDisabled={starLoading || !isConversationOpen}
            isStarred={isStarred}
            likeCount={likeCount}
            onLike={_onLike}
            onStar={_onStar}
          />
        </div>
      </div>
    </SpinnerError>
  );
};

export default ConversationStarred;
