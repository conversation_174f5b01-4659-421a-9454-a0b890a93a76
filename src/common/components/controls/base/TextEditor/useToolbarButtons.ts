import { useMemo } from 'react';
import { IModule } from '../../../../abstract/IModuleProps';
import { HIDDEN } from '../../../../../fsCategories';
import { ICustomButton } from './useCustomButtons';

const useToolbarButtons = ({
  isExtended,
  mergeCodes,
  fileCategory,
  hasMoreButton = true,
  customButtons = [],
}: {
  isExtended?: boolean;
  mergeCodes?: Array<IModule>;
  fileCategory?: string;
  hasMoreButton?: boolean;
  customButtons?: ICustomButton[];
}) =>
  useMemo(() => {
    const customButtonIds = customButtons.map(button => button.id);

    if (isExtended) {
      return {
        moreText: {
          buttons: [
            'bold',
            'italic',
            'strikeThrough',
            'subscript',
            'superscript',
            'fontFamily',
            'fontSize',
            'textColor',
            'backgroundColor',
            'inlineStyle',
            'clearFormatting',
          ],
          buttonsVisible: 3,
        },
        moreParagraph: {
          buttons: [
            'alignLeft',
            'alignCenter',
            'alignRight',
            'formatOL',
            'formatUL',
            'insertLink',

            'alignJustify',
            'paragraphFormat',
            'paragraphStyle',
            'lineHeight',
            'outdent',
            'indent',
            'quote',
          ],
          buttonsVisible: 5,
        },
        moreRich: {
          buttons: [
            ...(fileCategory && fileCategory !== HIDDEN ? ['insertImage'] : []),
            ...(mergeCodes ? ['mergeCodes'] : []),

            'specialCharacters',
            'insertTable',
            'insertHR',
          ],
          // eslint-disable-next-line no-magic-numbers
          buttonsVisible: mergeCodes ? 2 : 1,
        },
        moreMisc: {
          buttons: ['undo', 'redo', 'fullscreen', 'print', 'html', 'help'],
          align: 'right',
          buttonsVisible: 2,
        },
      };
    }

    return {
      moreText: {
        buttons: [
          'bold',
          'italic',
          'underline',
          ...(mergeCodes ? ['mergeCodes'] : []),
          ...(fileCategory && fileCategory !== HIDDEN ? ['insertImage'] : []),
        ],
        buttonsVisible: 5,
      },
      moreRich: hasMoreButton
        ? {
            buttons: [
              'specialCharacters',
              'insertTable',
              'insertHR',

              'strikeThrough',
              'subscript',
              'superscript',
              'fontFamily',
              'fontSize',
              'textColor',
              'backgroundColor',
              'inlineStyle',
              'clearFormatting',

              'alignLeft',
              'alignCenter',
              'alignRight',
              'formatOL',
              'formatUL',
              'insertLink',
              'alignJustify',
              'paragraphFormat',
              'paragraphStyle',
              'lineHeight',
              'outdent',
              'indent',
              'quote',

              'undo',
              'redo',
              'fullscreen',
              'print',
              'html',
              'help',
            ],
            buttonsVisible: 0,
            align: customButtonIds.length ? 'left' : 'right',
          }
        : {},
      moreMisc: {
        buttons: [...customButtonIds],
        align: 'right',
        buttonsVisible: customButtonIds.length,
      },
    };
  }, [hasMoreButton, fileCategory, mergeCodes, isExtended, customButtons]);

export default useToolbarButtons;
