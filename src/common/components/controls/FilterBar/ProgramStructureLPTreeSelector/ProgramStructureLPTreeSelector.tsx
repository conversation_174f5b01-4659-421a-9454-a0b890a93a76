import { isNumber, some, startsWith } from 'lodash';
import React, { useCallback, useState } from 'react';
import programStructureLPTreeGql from './data/programStructureLPTree.graphql';

import {
  resolveNodeId,
  resolveNodeParentId,
  resolveNodeTitle,
  isLearningPlanTask,
  isProgram,
  isProgramStructureSubject,
  isLearningPlan,
  isLearningPlanCategory,
  Program,
  LearningPlan,
  LearningPlanCategory,
  LearningPlanTask,
  ProgramStructureSubject,
} from './ProgramStructureLPTreeAdapter';

import nodeSortingOptionsSequence from '../../../../../common/utils/nodeSortingOptionsSequence';
import useResetOnDeps from '../../../../../common/components/controls/FilterBar/hooks/useResetOnDeps';
import GqlStaticTreeSelector from '../../../../../common/components/controls/FilterBar/GqlStaticTreeSelector';
import useT from '../../../../../common/components/utils/Translations/useT';
import { isRootNode } from '../../../../../common/components/dataViews/DynamicTree';
import Button from '../../../../../common/components/controls/Button';
import {
  SUBJECT,
  LEARNING_PLAN,
  LP_CATEGORY,
  LP_MODULE,
  CONTENT,
} from '../../../../../model/CurriculumStructureViewTypes';

const ProgramStructureLPTreeSelector = ({
  programGroupId,
  programGroupName,
  title,
  plugins = {},
  ...rest
}) => {
  const t = useT();
  const {
    onChange,
    selectionFilter,
    disabled,
    filter: { view },
  } = rest;
  const [models, setModels] = useState([]);

  useResetOnDeps({ programGroupId }, onChange, []);

  const renderNodeAddons = (node, meta) => {
    const { handleClear } = meta;

    const handleClearFn = e => {
      handleClear();
      e.preventDefault();
      e.stopPropagation();
      e.stopImmediatePropagation();
    };

    const addons: JSX.Element[] = [];
    if (isRootNode(node)) {
      addons.push(
        <Button
          additionClasses="text-primary no-padding-top no-padding-bottom"
          buttonStyle="link"
          onClick={handleClearFn}
        >
          {t('Clear')}
        </Button>,
      );
    }

    return addons;
  };

  const nodeIsLeaf = node => {
    switch (view) {
      case SUBJECT.value:
        return isProgram(node);
      case LEARNING_PLAN.value:
        return isProgramStructureSubject(node);
      case LP_CATEGORY.value:
        return isLearningPlan(node);
      case LP_MODULE.value:
        return isLearningPlanCategory(node);
      case CONTENT.value:
        return isLearningPlanTask(node);
      default:
        return true;
    }
  };

  const nodePrefix = () => {
    switch (view) {
      case SUBJECT.value:
        return Program;
      case LEARNING_PLAN.value:
        return ProgramStructureSubject;
      case LP_CATEGORY.value:
        return LearningPlan;
      case LP_MODULE.value:
        return LearningPlanCategory;
      case CONTENT.value:
        return LearningPlanTask;
      default:
        return '';
    }
  };

  const canRender = ({ id, model, leaf, nodesMeta: { hasChild }, state }) => {
    const checkLeafExistsRecursively = (nodeId: string): boolean => {
      if (!state.children[nodeId] || !state.children[nodeId].length) {
        return false;
      }

      const leafExists = some(state.children[nodeId], str =>
        startsWith(str, `${nodePrefix()}-`),
      );

      if (leafExists) {
        return true;
      }

      return some(state.children[nodeId], childId =>
        checkLeafExistsRecursively(childId),
      );
    };

    if ((model.id !== ':ROOT:' || model.id !== null) && !nodeIsLeaf(model)) {
      if (state.children[id]) {
        const leafExists = checkLeafExistsRecursively(id);

        if (!leafExists || leaf) {
          return false;
        }
      } else {
        return false;
      }
    }
    return true;
  };

  const resolveNodeClassName = node => {
    if (isRootNode(node)) {
      return 'jstree-node-bold';
    } else if (nodeIsLeaf(node)) {
      return 'jstree-node-italic';
    }
    return '';
  };

  const cookModels = useCallback(models => {
    setModels(models);
    return models;
  }, []);

  return (
    <GqlStaticTreeSelector
      hasFilter
      hasSyntheticRootNode
      adapter={{
        renderNodeAddons,
        resolveNodeId,
        resolveNodeParentId,
        resolveNodeTitle,
        resolveNodeClassName,
        canRender,
      }}
      cookModels={cookModels}
      disabled={!isNumber(programGroupId) || disabled || models.length === 0}
      emptyValueTitle={
        models.length === 0
          ? t('There is no data matching your criteria')
          : undefined
      }
      gql={programStructureLPTreeGql}
      gqlFetchPolicy="cache-first"
      gqlSkip={!isNumber(programGroupId)}
      gqlVariables={{ programGroupId, view }}
      hasClearButton={false}
      name="programStructureLP"
      plugins={{
        nodeSortingOptions: nodeSortingOptionsSequence,
        ...plugins,
      }}
      syntheticRootNodeName={t(programGroupName)}
      title={t(title)}
      onChange={onChange}
      {...rest}
    />
  );
};

export default ProgramStructureLPTreeSelector;
