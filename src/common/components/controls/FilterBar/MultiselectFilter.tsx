/* eslint-disable react/forbid-component-props */

import classnames from 'classnames';
import {
  filter,
  flatten,
  indexOf,
  isEmpty,
  map,
  orderBy,
  reduce,
  toLower,
  isString,
} from 'lodash';
import React, {
  PropsWithChildren,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';

import { TIcon } from '../../../propTypes';
import useBodyClick from '../../../utils/useBodyClick';
import Scroll from '../../other/Scroll';
import Icon, { TIconType } from '../../utils/Icon';
import Ripple from '../../utils/Ripple';
import useT from '../../utils/Translations/useT';
import SearchField from '../base/SearchField';
import { TButtonPosition } from '../Button';
import { IFilterBarComponentProps } from './FilterBar';
import useApplyFilter from './hooks/useApplyFilter';
import MultiselectGroup from './MultiselectGroup';
import MultiselectItem from './MultiselectItem';
import MultiselectSelectAllItem from './MultiselectSelectAllItem';

import styles from './Select.scss';

export default function MultiselectFilter<T>({
  value = [] as T[],
  onChange,
  title,
  label,
  icon,
  iconType,
  itemId = defaultItemId as any,
  itemTitle = defaultItemTitle as any,
  itemStatus = defaultItemStatus as any,
  showSelectAll = true,
  size = defaultSize,
  groupTitleKeys,
  orderOptionsBy,
  hasSelectAllDivider = true,
  opens = 'left',
  children,
  liveSearch = false,
  liveSearchField = 'name',
  disabled = false,
  isFullSizeButton = false,
  classNames = '',
  buttonClassName = '',
  options,
  isEmptyAllowed = false,
  showStatus = false,
  maxLimitTooltip = '',
  textTransformNone = false,
  labelDividerNone = false,
  inputClassName = '',
}: Readonly<PropsWithChildren<IMultiselectProps<T>>>) {
  const t = useT();

  const [expanded, setExpanded] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const containerEl = useRef<HTMLDivElement>(null);

  const handleBodyClick = useCallback(() => {
    setExpanded(false);
  }, []);

  useBodyClick<HTMLDivElement>({ handler: handleBodyClick, ref: containerEl });

  const handleToggleItem = useCallback(
    (id, selected) => {
      if (!onChange) {
        return;
      }
      if (selected) {
        onChange([...value, id]);
      } else {
        const index = indexOf(value, id);
        if (index >= 0) {
          onChange([
            ...value.slice(0, index),
            ...value.slice(index + 1),
          ] as T[]);
        }
      }
    },
    [value, onChange],
  );

  const handleToggleItems = useCallback(
    (items, selected) => {
      if (!onChange) {
        return;
      }

      const selectedIds = reduce(
        value,
        (set, id) => {
          set.add(id);
          return set;
        },
        new Set(),
      );

      for (const item of items) {
        if (selected) {
          selectedIds.add(itemId(item));
        } else {
          selectedIds.delete(itemId(item));
        }
      }

      onChange(Array.from(selectedIds) as T[]);
    },
    [value, itemId, onChange],
  );

  const handleToggleExpanded = useCallback(
    e => {
      e.preventDefault();
      setExpanded(!expanded);
    },
    [expanded],
  );

  const getGroupTitleKey = useCallback(
    groupIndex => {
      if (!groupTitleKeys || groupIndex >= groupTitleKeys.length) {
        return null;
      }
      return groupTitleKeys[groupIndex];
    },
    [groupTitleKeys],
  );

  const flatOptions = useMemo<Array<object>>(() => flatten<object>(options), [
    options,
  ]);

  const { applyFilters } = useApplyFilter();
  useEffect(() => {
    if (
      flatOptions?.length === 1 &&
      onChange &&
      isEmpty(value) &&
      !isEmptyAllowed
    ) {
      onChange(map(flatOptions, itemId));

      requestAnimationFrame(() => {
        applyFilters && applyFilters();
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [applyFilters, flatOptions, itemId, onChange, isEmptyAllowed]);

  const handleSelectAllToggle = useCallback(
    selectAll => {
      if (!onChange) {
        return;
      }

      if (selectAll) {
        onChange(map(flatOptions, item => itemId(item)) as T[]);
      } else {
        onChange([]);
      }
    },
    [itemId, onChange, flatOptions],
  );

  const handleSearchReset = () => setSearchQuery('');

  const checkLiveSearch = useCallback(
    option => {
      if (!liveSearch || !searchQuery) return true;

      const field = isString(liveSearchField)
        ? liveSearchField
        : liveSearchField(option);

      if (!option[field]) return false;

      return option[field]
        .toLocaleLowerCase()
        .includes(searchQuery.toLocaleLowerCase());
    },
    [searchQuery, liveSearch, liveSearchField],
  );

  const itemsById = useMemo<Map<T, TOption>>(
    () =>
      reduce(
        flatOptions,
        (map, item) => {
          map.set(itemId(item), item);
          return map;
        },
        new Map(),
      ),
    [itemId, flatOptions],
  );

  const selectedItems = useMemo<Array<TOption>>(
    () =>
      filter(
        map(value, id => itemsById.get(id as T)) as Array<TOption>,
        item => !!item,
      ),
    [value, itemsById],
  );

  const selectedIds = useMemo(() => new Set(value as Array<T>), [value]);

  const allSelected = useMemo(
    () => selectedItems.length === flatOptions.length,
    [selectedItems.length, flatOptions.length],
  );

  const valueTitle = useMemo(() => {
    if (!icon) {
      const limit = 3;
      if (selectedItems.length === 0) {
        return t('None Selected');
      } else if (allSelected) {
        return t('All');
      } else if (selectedItems.length > limit) {
        return t('#{count} selected', { count: selectedItems.length });
      }
      return selectedItems.map(item => itemTitle(item)).join(', ');
    }

    return '';
  }, [selectedItems, t, allSelected, icon, itemTitle]);

  const orderedOptions = useMemo(
    () =>
      orderOptionsBy
        ? [
            ...orderBy(
              filter(
                options,
                option => !Array.isArray(option) && checkLiveSearch(option),
              ),
              [option => option[orderOptionsBy].toLocaleLowerCase()],
              ['asc'],
            ),
            ...filter(options, option => Array.isArray(option)).map(
              optionsArray =>
                orderBy(
                  filter(optionsArray as Array<TOption>, option =>
                    checkLiveSearch(option),
                  ),
                  [option => toLower(option[orderOptionsBy]?.toString())],
                  ['asc'],
                ),
            ),
          ]
        : filter(options, checkLiveSearch),
    [options, orderOptionsBy, checkLiveSearch],
  );

  const isSelectAllShown = showSelectAll && isEmpty(searchQuery);

  return (
    <div
      ref={containerEl}
      className={classnames('dropdown status', classNames)}
    >
      <div
        className={classnames('btn-group', buttonClassName, {
          open: expanded,
          'w-100': isFullSizeButton,
        })}
      >
        <Ripple>
          <button
            aria-expanded={expanded}
            className="multiselect dropdown-toggle btn btn-default"
            disabled={disabled}
            title={label ? t(label) : ''}
            type="button"
            onClick={handleToggleExpanded}
          >
            <span className="multiselect-selected-text">
              {icon && <Icon name={icon} type={iconType} />}
              {title && <span className="text-semibold">{title}:&nbsp;</span>}
              {valueTitle && <span className="value ml-0">{valueTitle}</span>}
              <span className={classnames('caret', styles.selectCaret)} />
            </span>
          </button>
        </Ripple>
        <Scroll
          className={classnames(
            `dropdown-menu dropdown-menu-${opens} active`,
            styles.positionFix,
          )}
          classNameInner="multiselect-container dropdown-menu"
          isOpen={expanded}
          style={{ width: size }}
          /* eslint-disable-next-line react/forbid-component-props */
          tagName="ul"
        >
          {liveSearch && (
            <div className="search-field-wrapper">
              <SearchField
                isInTable
                className={styles.searchboxField}
                inputClassName={inputClassName}
                inputProps={{ autoFocus: true }}
                isDebouncingEnabled={false}
                value={searchQuery}
                widthClassName={styles.searchbox}
                onChange={setSearchQuery}
                onReset={handleSearchReset}
              />
            </div>
          )}
          {isSelectAllShown && [
            <MultiselectSelectAllItem
              key="selectAll"
              allSelected={allSelected}
              onToggle={handleSelectAllToggle}
            />,
            <li
              key="selectAllDivider"
              className={hasSelectAllDivider ? 'divider' : ''}
            />,
          ]}

          {map(
            orderedOptions,
            (item: TOption | Array<TOption>, itemIndex: number) => {
              if (Array.isArray(item)) {
                const groupId = `group-${map(item, itemId).join('-')}`;
                return (
                  <MultiselectGroup
                    key={groupId}
                    first={itemIndex === 0}
                    groupTitleKey={getGroupTitleKey(itemIndex)}
                    itemId={itemId}
                    items={item}
                    itemStatus={itemStatus}
                    itemTitle={itemTitle}
                    labelDividerNone={labelDividerNone}
                    maxLimitTooltip={maxLimitTooltip}
                    selectedIds={selectedIds}
                    showStatus={showStatus}
                    textTransformNone={textTransformNone}
                    onToggle={handleToggleItem}
                    onToggleGroup={handleToggleItems}
                  />
                );
              }

              const id = itemId(item);
              const title = itemTitle(item);
              const status = itemStatus(item);

              return (
                <MultiselectItem
                  key={`item-${id}`}
                  id={id}
                  maxLimitTooltip={maxLimitTooltip}
                  selected={id && selectedIds.has(id)}
                  showStatus={showStatus}
                  status={status}
                  title={title}
                  onToggle={handleToggleItem}
                />
              );
            },
          )}
          {children}
        </Scroll>
      </div>
    </div>
  );
}

interface IMultiselectProps<T> extends IFilterBarComponentProps<T[]> {
  title?: string;
  label?: string;
  icon?: TIcon;
  iconType?: TIconType;
  options: Array<object | object[]>;
  itemId?: (item: object) => T;
  itemTitle?: (item: TOption<T>) => string;
  itemStatus?: (item: TOption<T>) => string;
  showSelectAll?: boolean;
  hasSelectAllDivider?: boolean;
  size?: number;
  groupTitleKeys?: Array<string | null>;
  orderOptionsBy?: string;
  opens?: TButtonPosition;
  liveSearch?: boolean;
  liveSearchField?: string | ((item: T) => string);
  disabled?: boolean;
  isFullSizeButton?: boolean;
  classNames?: string;
  buttonClassName?: string;
  isEmptyAllowed?: boolean;
  showStatus?: boolean;
  maxLimitTooltip?: string;
  textTransformNone?: boolean;
  labelDividerNone?: boolean;
  inputClassName?: string;
}

export type TOption<T = number> = Record<string, string | number | boolean | T>;

const defaultItemTitle = ({ name }: TOption): string => name as string;

const defaultItemId = ({ id }: TOption) => id as string;

const defaultItemStatus = ({ status }: TOption) => status as string;

const defaultSize = 100;
