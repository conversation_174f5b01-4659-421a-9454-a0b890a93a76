import React, { FC, useCallback, useMemo } from 'react';
import classNames from 'classnames';

import { IFileAttachmentTemp } from '../../../../abstract/IFileAttachments';
import styles from './ImageGrid.scss';

interface IImageGrid {
  images: IFileAttachmentTemp[];
  onImageClick: (index: number) => void;
  className?: string;
  maxDisplayCount?: number;
}

const ImageGrid: FC<IImageGrid> = ({
  images,
  onImageClick,
  className,
  maxDisplayCount,
}) => {
  // Show all images by default, or limit if maxDisplayCount is specified
  const displayImages = useMemo(
    () => (maxDisplayCount ? images.slice(0, maxDisplayCount) : images),
    [images, maxDisplayCount],
  );

  const remainingCount = useMemo(
    () => (maxDisplayCount ? Math.max(0, images.length - maxDisplayCount) : 0),
    [images.length, maxDisplayCount],
  );

  const handleImageClick = useCallback(
    (index: number) => {
      onImageClick(index);
    },
    [onImageClick],
  );

  if (images.length === 0) {
    return null;
  }

  return (
    <div className={classNames(styles.imageGrid, className)}>
      {displayImages.map((image, index) => {
        const _handleImageClick = () => {
          handleImageClick(index);
        };
        return (
          <div
            key={image.fileId || image.uploadToken || index}
            className={classNames(styles.imageItem, {
              [styles.imageItemLast]:
                index === displayImages.length - 1 && remainingCount > 0,
            })}
            onClick={_handleImageClick}
          >
            <img
              alt={image.fileName}
              className={styles.image}
              loading="lazy"
              src={image.url}
            />

            {/* Overlay for remaining count */}
            {index === displayImages.length - 1 && remainingCount > 0 && (
              <div className={styles.remainingOverlay}>
                <span className={styles.remainingCount}>+{remainingCount}</span>
              </div>
            )}

            {/* Hover overlay */}
            <div className={styles.hoverOverlay}>
              <div className={styles.hoverContent}>
                <i className="icon-eye" />
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default ImageGrid;
