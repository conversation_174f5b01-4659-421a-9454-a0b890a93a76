.imageGrid {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  // border-radius: 8px;
  overflow: hidden;
  max-width: 100%;
  // border: 1px solid #e0e0e0;
}

.imageItem {
  position: relative;
  cursor: pointer;
  overflow: hidden;
  background: #f5f5f5;
  aspect-ratio: 1;
  transition: transform 0.2s ease;

  // Desktop: 4 images per row (col-lg-3 equivalent)
  flex: 0 0 calc(25% - 3px);

  &:hover {
    transform: scale(1.02);
    z-index: 1;

    .hoverOverlay {
      opacity: 1;
    }
  }
}

.image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
  transition: transform 0.2s ease;
}

.hoverOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.hoverContent {
  color: white;
  font-size: 24px;
  
  i {
    font-size: 24px;
  }
}

.remainingOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 18px;
}

.remainingCount {
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

// Responsive design
// Tablet: 3 images per row (col-md-4 equivalent)
@media (max-width: 1024px) and (min-width: 769px) {
  .imageItem {
    flex: 0 0 calc(33.333% - 2.67px);
  }
}

// Mobile: 2 images per row (col-sm-6 equivalent)
@media (max-width: 768px) {
  .imageGrid {
    max-width: 100%;
  }

  .imageItem {
    flex: 0 0 calc(50% - 2px);
  }

  .hoverContent {
    font-size: 20px;

    i {
      font-size: 20px;
    }
  }

  .remainingOverlay {
    font-size: 16px;
  }
}

@media (max-width: 480px) {
  .imageGrid {
    gap: 2px;
  }

  .imageItem {
    flex: 0 0 calc(50% - 1px);
  }

  .hoverContent {
    font-size: 18px;

    i {
      font-size: 18px;
    }
  }

  .remainingOverlay {
    font-size: 14px;
  }
}
