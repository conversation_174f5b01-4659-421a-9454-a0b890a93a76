.imagePreviewModal {
  :global(.modal-dialog) {
    max-width: 100vw;
    max-height: 100vh;
    margin: 0;
    width: 100%;
    height: 100%;
  }

  :global(.modal-content) {
    height: 100vh;
    border-radius: 0;
    border: none;
    background: #1e1e1e;
    color: white;
  }

  :global(.modal-body) {
    padding: 0;
    height: 100%;
    display: flex;
    flex-direction: column;
  }
}

.modalContent {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #1e1e1e;
  color: white;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  background: rgba(0, 0, 0, 0.8);
  border-bottom: 1px solid #333;
  z-index: 10;
  position: relative;
}

.leftControls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.rightControls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.controlButton {
  background: transparent;
  border: none;
  color: white;
  padding: 8px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.1);
  }

  i {
    font-size: 16px;
  }
}

.imageTitle {
  font-size: 14px;
  font-weight: 500;
  color: #e0e0e0;
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.separator {
  color: #666;
  margin: 0 4px;
}

.imageContainer {
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background: #1e1e1e;
}

.navButton {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(0, 0, 0, 0.6);
  border: none;
  color: white;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  cursor: pointer;
  z-index: 5;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    background: rgba(0, 0, 0, 0.8);
    transform: translateY(-50%) scale(1.1);
  }

  i {
    font-size: 20px;
  }
}

.navButtonLeft {
  left: 20px;
}

.navButtonRight {
  right: 20px;
}

.imageWrapper {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  position: relative;
}

.mainImage {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  transition: transform 0.2s ease;
  user-select: none;
  -webkit-user-drag: none;
}

.thumbnailStrip {
  background: rgba(0, 0, 0, 0.8);
  border-top: 1px solid #333;
  padding: 12px 20px;
  overflow-x: auto;
  overflow-y: hidden;
  
  &::-webkit-scrollbar {
    height: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
    
    &:hover {
      background: rgba(255, 255, 255, 0.5);
    }
  }
}

.thumbnailContainer {
  display: flex;
  gap: 8px;
  min-width: max-content;
}

.thumbnail {
  background: transparent;
  border: 2px solid transparent;
  border-radius: 4px;
  cursor: pointer;
  padding: 2px;
  transition: all 0.2s ease;
  flex-shrink: 0;

  &:hover {
    border-color: rgba(255, 255, 255, 0.5);
  }

  &.thumbnailActive {
    border-color: #0078d4;
    box-shadow: 0 0 0 1px #0078d4;
  }
}

.thumbnailImage {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 2px;
  display: block;
}

// Responsive design
@media (max-width: 768px) {
  .header {
    padding: 8px 12px;
  }

  .leftControls,
  .rightControls {
    gap: 6px;
  }

  .controlButton {
    padding: 6px;
    
    i {
      font-size: 14px;
    }
  }

  .navButton {
    width: 50px;
    height: 50px;
    
    i {
      font-size: 18px;
    }
  }

  .navButtonLeft {
    left: 10px;
  }

  .navButtonRight {
    right: 10px;
  }

  .thumbnailStrip {
    padding: 8px 12px;
  }

  .thumbnailImage {
    width: 50px;
    height: 50px;
  }

  .imageTitle {
    max-width: 200px;
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .rightControls {
    .controlButton {
      &:not(:last-child):not(:nth-last-child(2)) {
        display: none;
      }
    }
    
    .separator {
      display: none;
    }
  }

  .imageTitle {
    max-width: 150px;
    font-size: 12px;
  }
}
