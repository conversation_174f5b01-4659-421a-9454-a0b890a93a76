import React, { useCallback, useMemo, useState } from 'react';
import { useMutation } from 'react-apollo';
import {
  ceil,
  filter,
  isEmpty,
  isEqual,
  isNumber,
  map,
  sortBy,
  uniq,
} from 'lodash';
import classNames from 'classnames';
import getBlobDuration from 'get-blob-duration';
import RoundedPrimaryButton from '../../RoundedPrimaryButton';
import { IFileAttachmentTemp } from '../../../../abstract/IFileAttachments';
import RoundedLinkButton from '../../RoundedLinkButton';
import Modal from '../../../utils/Modal';
import useT from '../../../utils/Translations/useT';
import NumberField from '../../../controls/base/NumberField';
import Icon from '../../../utils/Icon';
import styles from './SplitAudioModal.scss';
import bahaiBeatsSplitAudioGql from './data/bahaiBeatsSplitAudio.graphql';
import Spinner from '../../../utils/Spinner';
import { handleFormErrors } from '../../../../../common/errors';
import Notifications from '../../../../utils/Notifications';
import { ONE } from '../../../../const';

const THREE = 3;
const SECOND_UNIT = 60;
const MINUTE_UNIT = 3600;
const THOUSAND = 1000;
const MIN_VALID_POINTS = 2;
const MAX_HOURS = 23;
const MAX_MINUTES = 59;
const MAX_SECONDS = 59;
const startingPoint = 0;

interface ITimeValue {
  hours: number | null;
  minutes: number | null;
  seconds: number | null;
}

interface ISplitAudioModal {
  isModalVisible: boolean;
  attachment: IFileAttachmentTemp;
  onSubmit: (
    attachment: IFileAttachmentTemp,
    splittedFiles: string[],
  ) => Promise<void>;
  setModalVisibility: (isVisible: boolean) => void;
}

const SplitAudioModal: React.FC<ISplitAudioModal> = ({
  onSubmit,
  attachment,
  isModalVisible = false,
  setModalVisibility,
}) => {
  const t = useT();
  const defaultRows: Array<{
    id: number;
    end: ITimeValue | string;
    src: string;
  }> = [
    {
      id: 1,
      end: { hours: null, minutes: null, seconds: null },
      src: '',
    },
  ];

  const [rows, setRows] = useState(defaultRows);
  const [uploading, setUploading] = useState<boolean>(false);
  const [submitDisabled, setSubmitDisabled] = useState<boolean>(true);
  const [gettingDuration, setGettingDuration] = useState<boolean>(false);

  const handleModalClose = useCallback(() => {
    setRows(defaultRows);
    setModalVisibility(false);
  }, [defaultRows, setModalVisibility]);

  const [splitAudioMutation, { loading: splitAudioLoading }] = useMutation(
    bahaiBeatsSplitAudioGql,
  );

  const handleSubmit = useCallback(async () => {
    setUploading(true);
    const splittedFiles = map(
      filter(rows, y => y.src !== ''),
      x => x.src,
    );
    await onSubmit(attachment, splittedFiles);
    setUploading(false);
    handleModalClose();
  }, [handleModalClose, onSubmit, attachment, rows]);

  const noRows = useMemo(() => !filter(rows, y => y.src !== '').length, [rows]);

  const getDuration = useCallback(async url => {
    setGettingDuration(true);
    const response = await fetch(url);
    const blob = await response.blob();
    const duration = (await getBlobDuration(blob)) * THOUSAND;
    setGettingDuration(false);
    return duration;
  }, []);

  const convertToSeconds = useCallback(timeObj => {
    // Handle both old string format and new object format for backward compatibility
    if (typeof timeObj === 'string') {
      const [hours, minutes, seconds] = timeObj.split(':').map(Number);
      return hours * MINUTE_UNIT + minutes * SECOND_UNIT + seconds;
    }
    // New object format - treat null as 0
    if (timeObj && typeof timeObj === 'object') {
      const hours = timeObj.hours || 0;
      const minutes = timeObj.minutes || 0;
      const seconds = timeObj.seconds || 0;
      return hours * MINUTE_UNIT + minutes * SECOND_UNIT + seconds;
    }
    return 0;
  }, []);

  const onProcessSplit = useCallback(async () => {
    setSubmitDisabled(true);
    const mappedBfr = rows.map(x => ({
      ...x,
      src: '',
    }));
    setRows(mappedBfr);
    const duration = attachment.duration || (await getDuration(attachment.url));
    const maxNumber = duration / THOUSAND;
    const validRows = rows.filter(row => {
      if (typeof row.end === 'object' && row.end) {
        return (
          (row.end.hours !== null && row.end.hours > 0) ||
          (row.end.minutes !== null && row.end.minutes > 0) ||
          (row.end.seconds !== null && row.end.seconds > 0)
        );
      }
      return row.end !== '';
    });
    const _validRows = validRows.map(row => {
      const seconds = convertToSeconds(row.end);
      return {
        ...row,
        end: seconds,
      };
    });
    const sortedEnds: number[] = [
      startingPoint as number,
      ...sortBy(_validRows.map(row => (row.end as unknown) as number)),
    ];
    if (sortedEnds[0] > maxNumber) {
      return Notifications.error(t('Error'), t('Start point is not valid.'), t);
    }
    const points: number[] = [];
    for (const num of sortedEnds) {
      if (num <= maxNumber) {
        points.push(num);
      } else {
        points.push(ceil(maxNumber));
        break; // Stop adding numbers after reaching the maximum
      }
    }
    if (points.length < MIN_VALID_POINTS) {
      return Notifications.error(
        t('Error'),
        t('Enter at least one split point.'),
        t,
      );
    }
    if (!isNumber(points[0])) {
      return Notifications.error(t('Error'), t('Start point is not valid.'), t);
    }
    const { data } = await handleFormErrors(
      splitAudioMutation({
        variables: {
          audioFile: attachment?.url,
          points,
        },
      }),
      t,
    );
    const mapped = rows.map(x => ({
      ...x,
      src:
        data?.bahaiBeatsSplitAudio?.find(
          y =>
            y.pointTo === convertToSeconds(x.end) ||
            (!isEmpty(x.end) &&
              convertToSeconds(x.end) > maxNumber &&
              y.pointTo === ceil(maxNumber)),
        )?.audioFile || '',
    }));
    setRows(mapped);
    setSubmitDisabled(false);
  }, [rows, attachment, splitAudioMutation, t, getDuration, convertToSeconds]);

  const hasError = useMemo(() => {
    const points = [
      startingPoint,
      ...rows.map(x => convertToSeconds(x.end)),
    ].filter(x => isNumber(x));
    return !(
      isEqual(points, sortBy(points)) && uniq(points).length === points.length
    );
  }, [rows, convertToSeconds]);

  const getLastPoint = useCallback(
    id => {
      if (id === 1) return startingPoint;
      const lastRow = rows.find(x => x.id === id - 1);
      if (!lastRow) return startingPoint;

      // Convert to seconds for comparison
      if (typeof lastRow.end === 'object' && lastRow.end) {
        return convertToSeconds(lastRow.end);
      }
      return lastRow.end || startingPoint;
    },
    [rows, convertToSeconds],
  );

  const maxAudioLength = useMemo(() => {
    const duration = attachment.duration;
    const maxNumber = duration ? duration / THOUSAND : null;
    return maxNumber;
  }, [attachment.duration]);

  const getErrorMessage = useCallback(
    (id, end) => {
      const max = maxAudioLength;

      // Handle new object format
      if (typeof end === 'object' && end) {
        // Validate individual components (only if they have values)
        if (end.hours !== null && (end.hours < 0 || end.hours > MAX_HOURS)) {
          return `Hours must be between 0 and ${MAX_HOURS}`;
        }
        if (
          end.minutes !== null &&
          (end.minutes < 0 || end.minutes > MAX_MINUTES)
        ) {
          return `Minutes must be between 0 and ${MAX_MINUTES}`;
        }
        if (
          end.seconds !== null &&
          (end.seconds < 0 || end.seconds > MAX_SECONDS)
        ) {
          return `Seconds must be between 0 and ${MAX_SECONDS}`;
        }

        const totalSeconds = convertToSeconds(end);
        const lastPointSeconds = getLastPoint(id);

        if (totalSeconds <= lastPointSeconds) {
          return `Value should be greater than ${lastPointSeconds} seconds`;
        }

        if (max && totalSeconds > max && id !== ONE) {
          return 'Invalid split point';
        }

        return undefined;
      }

      // Handle legacy string format for backward compatibility
      if (typeof end === 'string') {
        if (end.includes(':')) {
          if (end.split(':').length !== THREE) {
            return 'Invalid split point';
          }
          const seconds = convertToSeconds(end);
          const lastSeconds = getLastPoint(id);
          if (
            isNumber(seconds) &&
            isNumber(lastSeconds) &&
            seconds <= lastSeconds
          ) {
            return `Value should be greater than ${lastSeconds}`;
          } else if (max && seconds > max && id !== ONE) {
            return 'Invalid split point';
          }
        }

        if (!isEmpty(end) && Number(end) <= Number(getLastPoint(id))) {
          return `Value should be greater than ${getLastPoint(id)}`;
        } else if (max && Number(end) > max && id !== ONE) {
          return 'Invalid split point';
        }
      }

      return undefined;
    },
    [getLastPoint, maxAudioLength, convertToSeconds],
  );

  const isError = useMemo(
    () => rows.some(row => !!getErrorMessage(row.id, row.end)),
    [rows, getErrorMessage],
  );

  const renderModalFooter = useCallback(
    () => (
      <React.Fragment>
        <hr className={styles.modalHr} />
        <RoundedLinkButton
          additionClasses="mt-5 no-padding-left"
          onClick={handleModalClose}
        >
          {t('Cancel')}
        </RoundedLinkButton>
        <RoundedPrimaryButton
          additionClasses="mt-5"
          disabled={splitAudioLoading || gettingDuration || hasError || isError}
          onClick={onProcessSplit}
        >
          {splitAudioLoading || gettingDuration ? (
            <Spinner inline />
          ) : (
            t('Process Split')
          )}
        </RoundedPrimaryButton>
        <RoundedPrimaryButton
          additionClasses="mt-5"
          disabled={submitDisabled || uploading || noRows}
          onClick={handleSubmit}
        >
          {uploading ? <Spinner inline /> : t('Confirm')}
        </RoundedPrimaryButton>
      </React.Fragment>
    ),
    [
      gettingDuration,
      noRows,
      handleModalClose,
      onProcessSplit,
      handleSubmit,
      t,
      splitAudioLoading,
      uploading,
      submitDisabled,
      hasError,
      isError,
    ],
  );

  const handleTimeComponentChange = useCallback(
    (
      id: number,
      component: 'hours' | 'minutes' | 'seconds',
      value: number | null,
    ) => {
      setRows(prevRows => {
        const updatedRows = prevRows.map(row => {
          if (row.id === id) {
            const currentEnd =
              typeof row.end === 'object'
                ? row.end
                : { hours: null, minutes: null, seconds: null };
            return {
              ...row,
              end: {
                ...currentEnd,
                [component]: value,
              },
            };
          }
          return row;
        });

        const isLastRow = id === prevRows[prevRows.length - 1].id;
        const currentRow = updatedRows.find(row => row.id === id);
        const hasAnyValue =
          currentRow &&
          typeof currentRow.end === 'object' &&
          ((currentRow.end.hours !== null && currentRow.end.hours > 0) ||
            (currentRow.end.minutes !== null && currentRow.end.minutes > 0) ||
            (currentRow.end.seconds !== null && currentRow.end.seconds > 0));

        if (isLastRow && hasAnyValue) {
          updatedRows.push({
            id: prevRows.length + 1,
            end: { hours: null, minutes: null, seconds: null },
            src: '',
          });
        }

        return updatedRows;
      });
    },
    [],
  );

  // Memoized handlers to avoid creating new functions on every render
  const timeChangeHandlers = useMemo(() => {
    const handlers: Record<
      number,
      {
        handleHours: (value: number) => void;
        handleMinutes: (value: number) => void;
        handleSeconds: (value: number) => void;
      }
    > = {};

    rows.forEach(row => {
      handlers[row.id] = {
        handleHours: (value: number) =>
          handleTimeComponentChange(row.id, 'hours', value || null),
        handleMinutes: (value: number) =>
          handleTimeComponentChange(row.id, 'minutes', value || null),
        handleSeconds: (value: number) =>
          handleTimeComponentChange(row.id, 'seconds', value || null),
      };
    });

    return handlers;
  }, [rows, handleTimeComponentChange]);

  const handleRemoveRow = (id: number) => {
    setRows(prevRows => {
      const p = [...prevRows];
      const filtered = p.filter(row => row.id !== id);
      if (!filtered.length) return defaultRows;
      const mapped = filtered.map((x, idx) => ({
        ...x,
        id: idx + 1,
      }));
      return mapped;
    });
  };

  return (
    <Modal
      renderFooter={renderModalFooter}
      title={t('Split Audio')}
      visible={isModalVisible}
      onClose={handleModalClose}
    >
      <div>
        {rows.map(row => {
          const onRemove = () => {
            handleRemoveRow(row.id);
          };

          const timeValue =
            typeof row.end === 'object'
              ? row.end
              : { hours: null, minutes: null, seconds: null };
          const errorMessage = getErrorMessage(row.id, row.end);

          const handlers = timeChangeHandlers[row.id] || {
            handleHours: () => {
              /* fallback */
            },
            handleMinutes: () => {
              /* fallback */
            },
            handleSeconds: () => {
              /* fallback */
            },
          };

          return (
            <div
              key={row.id}
              className={classNames(styles.wrapper, 'row mb-10 pl-5 pr-5')}
            >
              <div className="col-lg-4">
                <div className={styles.timeWrapper}>
                  <div className="col-4">
                    <NumberField
                      errorMessage={errorMessage}
                      label={t('HH')}
                      max={MAX_HOURS}
                      min={0}
                      placeholder="hh"
                      value={timeValue.hours}
                      onChange={handlers.handleHours}
                    />
                  </div>
                  <div className="col-4">
                    <NumberField
                      label={t('MM')}
                      max={MAX_MINUTES}
                      min={0}
                      placeholder="mm"
                      value={timeValue.minutes}
                      onChange={handlers.handleMinutes}
                    />
                  </div>
                  <div className="col-4">
                    <NumberField
                      label={t('SS')}
                      max={MAX_SECONDS}
                      min={0}
                      placeholder="ss"
                      value={timeValue.seconds}
                      onChange={handlers.handleSeconds}
                    />
                  </div>
                </div>
              </div>
              {row.src ? (
                <div className="col-lg-7">
                  <audio
                    controls
                    controlsList="nodownload noplaybackrate"
                    src={row.src}
                  />
                </div>
              ) : (
                <div className={classNames(styles.emptyWidth, 'col-lg-7')} />
              )}
              <div className="col-lg-1">
                <Icon
                  className="pull-right text-danger-600"
                  name="cross3 cursor-pointer"
                  onClick={onRemove}
                />
              </div>
            </div>
          );
        })}
      </div>
    </Modal>
  );
};

export default SplitAudioModal;
