.main {
  border: 1px solid #D7D7D7;
  width: 150px;
  height: 170px;
  margin: 0 10px 10px 0;
  float: left;

  :global(.dz-preview) {

    :global(.dz-image) {
      position: relative;
      width: 100%;
      padding-bottom: 60%;
      overflow: hidden;
      height: 100px;

      > * {
        position: absolute;
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
        max-width: 100%;
        width: auto;
        object-fit: cover;
        object-position: center;
        height: 100%;
        margin: auto;
        display: block;
      }

      :global(.mediaIcon) {
        height: 100% !important;
        display: flex !important;
      }

      :global(.spinner) {
        left: calc(50% - 8px);
        top: calc(50% - 8px);
      }
    }
  }
}
.cardHeader {
  margin-top: 10px;
  display: flex;
  justify-content: space-between;
  padding: 0 5px;
}

.cardName {
  font-size: 12px;
}

.icon {
  width: 100%;
  font-size: 40px;
  padding: 25px 10px;
  cursor: pointer;
}

.previewIcon {
  font-size: 80px;
}

.previewBody {
  display: flex;
}

.link {
  color: black !important;
  text-decoration: none;
  display: block;
  width: 100%;

  &:visited,:link,:hover,:active {
    color: black;
    text-decoration: none;
  }
}

.itemWrapper {
  >a {
    cursor: auto !important;
  }
}
