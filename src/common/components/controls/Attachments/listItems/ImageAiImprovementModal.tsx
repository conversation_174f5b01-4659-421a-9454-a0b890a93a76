import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useQuery } from 'react-apollo';
import { filter, get, isEmpty, map, orderBy } from 'lodash';
import classNames from 'classnames';
import RoundedPrimaryButton from '../../RoundedPrimaryButton';
import { IFileAttachmentTemp } from '../../../../abstract/IFileAttachments';
import RoundedLinkButton from '../../RoundedLinkButton';
import Modal from '../../../utils/Modal';
import useT from '../../../utils/Translations/useT';
import styles from './ImageAiImprovementModal.scss';
import Spinner from '../../../utils/Spinner';
import Notifications from '../../../../utils/Notifications';
import Scroll from '../../../../../common/components/other/Scroll';
import Text<PERSON>reaField from '../../../containers/EntityForm/fields/TextAreaField';
import getGqlOperationName from '../../../../utils/getGqlOperationName';
import dalleQueryResultGql from '../../../../../common/data/eContent/dalleQueryResult.graphql';
import eContentSetting from '../../../../../common/data/eContent/eContentSetting.graphql';
import {
  GPT_VERSION,
  IMAGE_AI_IMPROVEMENT,
} from '../../../../../model/SettingType';
import { PanelButton } from '../../PanelButtons';
import SelectBoxField from '../../../containers/EntityForm/fields/SelectBoxField';
import { IGptVersion } from '../../../../../modules/EContent/EContentLibraries/EContentLibraryItems/form/tabs/EContentItemContentsTab/form/TextTransform';
import useEntityFormContext from '../../../containers/EntityForm/internal/useEntityFormContext';
import useUserPreferences from '../../../../data/hooks/useUserPreferences';
import FilePreview from '../../UploadFileDialog/FilePreview';
import Radio from '../../base/Radio';

const FIFTEEN = 15;
const PROMPT_TEXT_LENGTH1 = 1000;
const THREE = 3;
const BUTTON_MORE_NUMBER = 5;

interface IImageAiImprovementModal {
  isModalVisible: boolean;
  attachment: IFileAttachmentTemp;
  onSubmit: (selectedImage: string) => Promise<void>;
  setModalVisibility: (isVisible: boolean) => void;
}

const ImageAiImprovementModal: React.FC<IImageAiImprovementModal> = ({
  onSubmit,
  attachment,
  isModalVisible = false,
  setModalVisibility,
}) => {
  const t = useT();
  const {
    preferences: { E_CONTENT_TEXT_TRANSFORM_GPT_VERSION },
    updatePreferences,
  } = useUserPreferences();

  const [selectedButton, setSelectedButton] = useState(0);
  const [aiModel, setAiModel] = useState<string>();
  const { values, setFieldValue } = useEntityFormContext();
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [responseText, setResponseText] = useState<string[]>([]);
  const [counter, setCounter] = useState<number>(0);
  const [selectedImage, setSelectedImage] = useState<string>();
  const [uploading, setUploading] = useState<boolean>(false);
  const [numberOfitemsShown, setNumberOfItemsToShown] = useState(
    BUTTON_MORE_NUMBER,
  );

  const handleRadioChange = useCallback((val: string) => {
    setSelectedImage(val);
  }, []);

  const onClearInput = useCallback(() => {
    setFieldValue('inputAuto', '');
  }, []);
  const onClearImages = useCallback(() => {
    setSelectedImage(undefined);
    setResponseText([]);
  }, []);

  const windowHeight = useMemo(() => {
    const six = 0.5;
    const two = 0.2;
    const twoTwo = 0.23;
    const twoFive = 0.25;
    const four = 0.4;
    const fourFive = 0.45;
    const seven = 0.6;
    const fifty = 60;
    const height = window.innerHeight;
    setNumberOfItemsToShown(Math.round((six * height) / fifty));
    return {
      h20: Math.round(two * height),
      h22: Math.round(twoTwo * height),
      h25: Math.round(twoFive * height),
      h40: Math.round(four * height),
      h45: Math.round(fourFive * height),
      h60: Math.round(six * height),
      h70: Math.round(seven * height),
    };
  }, []);

  const onChangeVersion = useCallback(
    item => {
      setAiModel(item);
      updatePreferences({
        E_CONTENT_TEXT_TRANSFORM_GPT_VERSION: item,
      });
    },
    [updatePreferences],
  );

  const handleModalClose = useCallback(() => {
    setModalVisibility(false);
  }, [setModalVisibility]);

  const {
    data: dataVersion,
    error: errorVersion,
    loading: loadingVersion,
  } = useQuery(eContentSetting, {
    variables: { settingType: GPT_VERSION, searchQuery: '' },
    skip: isEmpty(GPT_VERSION),
  });

  const versionItems = useMemo(() => {
    if (dataVersion) {
      const field = getGqlOperationName(eContentSetting);
      const data = get(dataVersion, field, '');
      const res = get(data, 'response', []);
      return orderBy(
        map(res, item => {
          const contentList = item.split('\n');
          const itemObj: IGptVersion = {
            name: contentList[0].split('=').pop().trim(),
            value: contentList[1].split('=').pop().trim(),
            sequence: Number(contentList[2].split('=').pop().trim()),
            default: contentList[3].split('=').pop().trim() === 'true',
          };
          return itemObj;
        }),
        'sequence',
      );
    }
    return [];
  }, [dataVersion]);

  const gptVersion = useMemo(() => {
    const value =
      aiModel ||
      E_CONTENT_TEXT_TRANSFORM_GPT_VERSION ||
      versionItems?.find(x => !!x.default)?.value ||
      '';
    setFieldValue('gptVersion', value);
    return value as string;
  }, [aiModel, versionItems, E_CONTENT_TEXT_TRANSFORM_GPT_VERSION]);

  const { data: queryRes, error, loading } = useQuery(dalleQueryResultGql, {
    variables: {
      searchQuery,
      version: gptVersion,
      counter,
    },
    skip: isEmpty(searchQuery) || isEmpty(gptVersion),
  });

  useEffect(() => {
    if (!loading && queryRes) {
      setSearchQuery('');
      const field = getGqlOperationName(dalleQueryResultGql);
      const data = get(queryRes, field, '');
      if (!data) return;
      const jsonStr = get(data, 'response', '');
      if (!jsonStr) {
        Notifications.error(
          t('#{model} API key is not working.', {
            model: gptVersion === 'gemini-1.5-pro' ? 'Gemini' : 'OpenAI',
          }),
          '',
          t,
        );
      }
      setResponseText(prev => [jsonStr, ...prev]);
    }
  }, [queryRes, error, t, gptVersion, loading]);

  useEffect(() => {
    if (error && !loading) {
      setSearchQuery('');
      if (error.message.includes('API key not valid')) {
        Notifications.error(
          t('#{model} API key is not working.', {
            model: gptVersion === 'gemini-1.5-pro' ? 'Gemini' : 'OpenAI',
          }),
          '',
          t,
        );
      } else {
        Notifications.error(
          t('Data fetch failed, Please try again later!'),
          '',
          t,
        );
      }
    }
  }, [error, t, gptVersion, loading]);

  const { data: settingRes, error: errorSetting } = useQuery(eContentSetting, {
    variables: { settingType: IMAGE_AI_IMPROVEMENT, searchQuery: 'button' },
    skip: isEmpty(IMAGE_AI_IMPROVEMENT),
  });
  const settingItems = useMemo(() => {
    if (settingRes) {
      const field = getGqlOperationName(eContentSetting);
      const data = get(settingRes, field, '');
      const res = get(data, 'response', []);

      return orderBy(
        map(res, (item, index: number) => {
          const contentList = item.split('\n');
          let itemObj = {
            button: '',
            id: 0,
            description: 0,
            prompt: '',
          };
          if (contentList.length >= THREE) {
            itemObj = {
              id: index + 1,
              button: contentList[0].split('=').pop().trim(),
              description: contentList[1].split('=').pop().trim(),
              prompt: contentList[2].split('=').pop().trim(),
            };
          }
          return itemObj;
        }),
        'sequence',
      );
    }
    return [];
  }, [settingRes]);

  const handleSubmit = useCallback(async () => {
    setUploading(true);
    await onSubmit(selectedImage as string);
    setUploading(false);
    handleModalClose();
  }, [onSubmit, selectedImage, handleModalClose]);

  const renderModalFooter = useCallback(
    () => (
      <React.Fragment>
        <hr className={styles.modalHr} />
        <RoundedLinkButton
          additionClasses="mt-5 no-padding-left"
          onClick={handleModalClose}
        >
          {t('Cancel')}
        </RoundedLinkButton>
        <RoundedPrimaryButton
          additionClasses="mt-5"
          disabled={isEmpty(responseText) || !selectedImage || uploading}
          onClick={handleSubmit}
        >
          {uploading ? <Spinner inline /> : t('Attach')}
        </RoundedPrimaryButton>
      </React.Fragment>
    ),
    [responseText, selectedImage, handleModalClose, t, handleSubmit, uploading],
  );

  const gptVersionDropdown = useCallback(
    () => (
      <div className="w-100">
        <SelectBoxField
          columns={0}
          itemTitlePropName="name"
          itemValuePropName="value"
          label={t('Version')}
          name="gptVersion"
          options={versionItems}
          onChange={onChangeVersion}
        />
      </div>
    ),
    [versionItems, onChangeVersion, t],
  );

  const showMore = useCallback(() => {
    if (numberOfitemsShown + BUTTON_MORE_NUMBER <= settingItems.length) {
      setNumberOfItemsToShown(numberOfitemsShown + BUTTON_MORE_NUMBER);
    } else {
      setNumberOfItemsToShown(settingItems.length);
    }
  }, [numberOfitemsShown, settingItems]);

  const generateImage = useCallback(() => {
    if (selectedButton > 0) {
      const item = filter(
        settingItems,
        element => element.id === selectedButton,
      ).pop();
      const query = `${values?.inputAuto} ${item?.prompt}`;
      query === searchQuery && setCounter(counter => counter + 1);
      query && setSearchQuery(query);
    }
  }, [searchQuery, setSearchQuery, settingItems, values, selectedButton]);

  const addButtonList = useMemo(
    () => (
      <div className={styles.centerBox}>
        <Scroll autoHeightMax={windowHeight.h45}>
          <div className={styles.centerBox}>
            {map(settingItems.slice(0, numberOfitemsShown), element => {
              const onClickButton = function () {
                setSelectedButton(element.id);
              };
              return loading && selectedButton === element.id ? (
                <Spinner inline />
              ) : (
                <PanelButton
                  key={element.id}
                  badge={t(element.button)}
                  className={classNames(
                    'mb-10 mt-10 no-margin-left',
                    selectedButton === element.id
                      ? styles.active
                      : styles.inactive,
                  )}
                  icon=""
                  isDisabled={!!loading}
                  title={t((element.description as unknown) as string)}
                  onClick={onClickButton}
                />
              );
            })}
          </div>
        </Scroll>
        <RoundedLinkButton
          additionClasses={classNames(
            'btn btn-link legitRipple mr-5',
            settingItems &&
              numberOfitemsShown >= settingItems.length &&
              'invisible',
          )}
          type="button"
          onClick={showMore}
        >
          {t('More')}
        </RoundedLinkButton>
        {gptVersionDropdown()}
        <RoundedPrimaryButton
          additionClasses="mt-5"
          disabled={!!loading || !selectedButton || isEmpty(values?.inputAuto)}
          onClick={generateImage}
        >
          {loading ? <Spinner inline /> : t('Generate')}
        </RoundedPrimaryButton>
      </div>
    ),
    [
      generateImage,
      settingItems,
      selectedButton,
      loading,
      gptVersionDropdown,
      numberOfitemsShown,
      showMore,
      t,
      windowHeight.h45,
      values,
    ],
  );

  return (
    <Modal
      renderFooter={renderModalFooter}
      size="lg"
      title={t('Image AI Improvement')}
      visible={isModalVisible}
      onClose={handleModalClose}
    >
      <div>
        <div>
          <div className={styles.flexClear}>
            <RoundedLinkButton
              additionClasses="btn btn-link legitRipple btn-xs pull-left"
              type="button"
              onClick={onClearInput}
            >
              {t('Clear')}
            </RoundedLinkButton>
            <RoundedLinkButton
              additionClasses="btn btn-link legitRipple btn-xs pull-riht"
              type="button"
              onClick={onClearImages}
            >
              {t('Clear')}
            </RoundedLinkButton>
          </div>
          <div className={classNames(styles.questionTop)}>
            <div
              className={classNames(
                styles.col_40,
                'col-md-4 col-md-4 col-sm-4 col-xs-12',
                'p-10',
                styles.autoTextBox,
                styles.border,
              )}
            >
              <Scroll autoHeightMax={windowHeight.h70}>
                <TextAreaField
                  autoHeight
                  noLabel
                  className={styles.h60}
                  columns={1}
                  maxLength={PROMPT_TEXT_LENGTH1}
                  name="inputAuto"
                  placeholder={t('Enter prompt')}
                  rows={FIFTEEN}
                />
              </Scroll>
            </div>
            <div
              className={classNames(
                styles.col_10,
                'col-md-2 col-sm-3 col-xs-12',
                'p-10',
                styles.border,
              )}
            >
              {addButtonList}
            </div>
            <div
              className={classNames(
                styles.col_50,
                'col-md-6 col-sm-5 col-xs-12',
                'p-5',
                styles.border,
              )}
            >
              <Scroll autoHeightMax={windowHeight.h70}>
                <div className="">
                  {map(responseText, image => (
                    <div className={styles.flex}>
                      <div className="col-lg-1">
                        <Radio
                          value={selectedImage === image}
                          // eslint-disable-next-line react/jsx-no-bind
                          onChange={() => handleRadioChange(image)}
                        />
                      </div>
                      <div className="col-lg-8">
                        <div className={styles.wrapper}>
                          <div className={styles.card}>
                            <a href={image}>
                              <FilePreview
                                hideDetail
                                hideFilename
                                columns={4}
                                // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
                                // @ts-ignore
                                file={{ url: image }}
                                hasCrop={false}
                                hasRemove={false}
                              />
                            </a>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </Scroll>
            </div>
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default ImageAiImprovementModal;
