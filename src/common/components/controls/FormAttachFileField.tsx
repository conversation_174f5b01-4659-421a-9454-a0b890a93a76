import React, { FC } from 'react';
import { IFileAttachmentTemp } from '../../abstract/IFileAttachments';
import { TIcon } from '../../propTypes';
import AttachFile from './base/AttachFile';
import useStorageSettings from './base/TextEditor/useStorageSettings';
import useCurrentUser from '../../data/hooks/useCurrentUser';

const FormAttachFileField: FC<IFormAttachFileFieldProps> = ({
  onChange,
  value,
  fileCategory,
  isEditable,
  defaultIcon,
}) => {
  const {
    me: { organisationGroupId, tenantId },
  } = useCurrentUser();

  const { maxSize } = useStorageSettings(
    fileCategory,
    tenantId,
    organisationGroupId,
  );

  return (
    <AttachFile
      defaultIcon={defaultIcon}
      fileCategory={fileCategory}
      isEditable={isEditable}
      maxSize={maxSize}
      value={value}
      onChange={onChange}
    />
  );
};

export default FormAttachFileField;

interface IFormAttachFileFieldProps {
  value?: IFileAttachmentTemp;
  onChange?: (value?: IFileAttachmentTemp) => void;
  fileCategory: string;
  isEditable?: boolean;
  defaultIcon?: TIcon;
}
