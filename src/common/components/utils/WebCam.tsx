/* global process */
import React from 'react';

import Error from '../../../common/components/utils/Error';
import { ErrorWithHumanMessage } from '../../errors';

import withTranslations, {
  ITranslationContextProps,
} from '../../../common/components/utils/Translations/withTranslations';
import styles from './WebCam.scss';

export type TWebCam = typeof WebCam.defaultProps & {
  onError: Function;
  play?: boolean;
  takePhoto?: boolean;
  mediaConfig?: object;
  takePicture: Function;
  containerElement?: string;
  containerClassName?: string;
} & ITranslationContextProps;

type TState = {
  error: string | null;
};

class WebCam extends React.Component<TWebCam, TState> {
  static defaultProps = {
    mediaConfig: {
      audio: false,
      video: { facingMode: 'user' },
    },
    takePhoto: false,
    play: false,
    containerElement: 'div',
    containerClassName: '',
  };

  state = {
    error: null,
  };

  componentDidMount = async () => {
    this.props.play ? await this.startCamera() : this.stopCamera();
  };

  componentDidUpdate(prevProps) {
    if (prevProps !== this.props) {
      if (this.props.play !== prevProps.play) {
        this.props.play ? this.startCamera() : this.stopCamera();
      }
      if (this.props.takePhoto !== prevProps.takePhoto) {
        this.props.takePhoto ? this.takePicture() : false;
      }
    }
  }

  componentWillUnmount() {
    this.stopCamera();
  }

  webCamStream: MediaStream | null = null;
  video: HTMLVideoElement | null = null;

  stopCamera = () => {
    if (this.video) {
      this.video.pause();
      this.video.src = '';
    }
    if (this.webCamStream) {
      this.webCamStream.getTracks()[0].stop();
    }
  };

  startCamera = () => {
    const { mediaConfig, t, onError } = this.props;
    if (navigator && navigator.mediaDevices && process.env.BROWSER) {
      navigator.mediaDevices
        .getUserMedia(mediaConfig)
        .then(stream => {
          this.setState({ error: null });
          if ('srcObject' in this.video!) {
            this.video!.srcObject = stream;
          } else {
            // Avoid using this in new browsers, as it is going away.
            this.video!.src = URL.createObjectURL(stream);
          }
          this.webCamStream = stream;
          this.video!.play();
        })
        .catch(error => {
          this.setState({
            error: 'camera-is-banned-or-not-supported',
          });
          onError(error);
        });
    }
  };

  takePicture = () => {
    if (process.env.BROWSER) {
      const canvas = document.createElement('canvas');
      canvas.height = 475;
      canvas.width = 650;
      const ctx = canvas.getContext('2d')!;
      ctx.drawImage(this.video as CanvasImageSource, 0, 0);
      this.props.takePicture(canvas);
      this.stopCamera();
    }
  };

  render = () => {
    const { play, containerElement, containerClassName } = this.props;
    const { error } = this.state;
    const humanError = error ? new ErrorWithHumanMessage(error) : null;

    return (
      play &&
      React.createElement(
        containerElement,
        { className: containerClassName },
        humanError ? (
          <Error error={humanError} />
        ) : (
          <video
            ref={video => (this.video = video)}
            className={styles.WebCam}
          />
        ),
      )
    );
  };
}

export default withTranslations(WebCam);
