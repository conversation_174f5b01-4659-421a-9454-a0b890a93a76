import React, { FC } from 'react';
import Attachments from '../../../controls/Attachments';

import EntityField from '../internal/EntityField';
import {
  TImagesMime,
  allowedFileTypes as allowedMimeTypes,
} from '../../../../propTypes';
import { DEFAULT_WRAPPER_CLASS_NAMES } from '../internal/EntityFieldContent';

const AttachmentField: FC<IAttachmentFieldProps> = ({
  isEditable = false,
  hideAdd = false,
  hasWrapper = true,
  isInPage = false,
  isMultiple = true,
  onOpen = null,
  onClose = null,
  isDownloadable = false,
  isSubsection = false,
  hasRoutes = false,
  isListHidden = false,
  onCancelButtonText = 'Cancel',
  onCancelButtonStyle = 'link',
  allowedNumberOfFiles = 0,
  minimumNumberOfFiles = 0,
  required = false,
  isReadOnly = false,
  addButtonPosition = 'TOP',
  attachmentListComponent = null,
  addAttachmentComponent = null,
  allowedFileTypes = allowedMimeTypes,
  noWrapperStyle = false,
  isAddCentered = false,
  organisationGroupId = null,
  tenantId = null,
  strikeTitle,
  strikeIcon,
  name,
  categoryKey,
  attachmentItemComponent,
  hasVideoRecorder,
  hasAudioRecorder,
  hasPhotoRecorder,
  maxSize,
  maxDuration,
  addButtonTitle,
  isThreadAttachment,
  hasLowStrike,
  ...rest
}) => (
  <EntityField name={name} {...rest}>
    {(editable, field, { disabled }) => {
      const {
        input: { value, onChange },
        meta: { error, touched, submitFailed, submitting },
      } = field;

      return (
        <Attachments
          addAttachmentComponent={!hideAdd && addAttachmentComponent}
          addButtonPosition={addButtonPosition}
          addButtonTitle={addButtonTitle}
          allowedFileTypes={allowedFileTypes}
          allowedNumberOfFiles={allowedNumberOfFiles}
          attachmentItemComponent={attachmentItemComponent}
          attachmentListComponent={attachmentListComponent}
          attachments={value || []}
          categoryKey={categoryKey}
          editable={!isReadOnly && (editable || isEditable)}
          errorMessage={((touched || submitFailed) && error) || null}
          hasAudioRecorder={hasAudioRecorder}
          hasLowStrike={hasLowStrike}
          hasPhotoRecorder={hasPhotoRecorder}
          hasRoutes={hasRoutes}
          hasVideoRecorder={hasVideoRecorder}
          hasWrapper={hasWrapper}
          isAddCentered={isAddCentered}
          isDisabled={disabled || submitting}
          isDownloadable={isDownloadable}
          isInPage={isInPage}
          isListHidden={isListHidden}
          isMultiple={isMultiple}
          isSubsection={isSubsection}
          isThreadAttachment={isThreadAttachment}
          maxDuration={maxDuration}
          maxSize={maxSize}
          minimumNumberOfFiles={minimumNumberOfFiles}
          name={strikeIcon}
          noWrapperStyle={noWrapperStyle}
          organisationGroupId={organisationGroupId}
          required={required}
          strikeTitle={strikeTitle}
          tenantId={tenantId}
          onCancelButtonStyle={onCancelButtonStyle}
          onCancelButtonText={onCancelButtonText}
          onClose={onClose}
          onOpen={onOpen}
          onUpdate={onChange}
        />
      );
    }}
  </EntityField>
);

interface IAttachmentFieldProps {
  name: string;
  categoryKey: string;
  isEditable?: boolean;
  hideAdd?: boolean;
  hasWrapper?: boolean;
  isInPage?: boolean;
  onOpen?: Function;
  onClose?: Function;
  isMultiple?: boolean;
  isDownloadable?: boolean;
  isSubsection?: boolean;
  hasRoutes?: boolean;
  isListHidden?: boolean;
  onCancelButtonText?: string;
  onCancelButtonStyle?: 'link' | 'bordered';
  allowedNumberOfFiles?: number;
  minimumNumberOfFiles?: number;
  required?: boolean;
  isReadOnly?: boolean;

  organisationGroupId?: number;
  tenantId?: number;

  addButtonPosition?: 'TOP' | 'BOTTOM';
  attachmentListComponent?: Function;
  attachmentItemComponent?: Function;
  addAttachmentComponent?: Function;
  allowedFileTypes?: '' | TImagesMime | string | string[];
  noWrapperStyle?: boolean;
  isAddCentered?: boolean;
  columns?: number;
  maxSize?: number;
  maxDuration?: number;
  strikeTitle?: string;
  strikeIcon?: string;
  hasVideoRecorder?: boolean;
  hasAudioRecorder?: boolean;
  hasPhotoRecorder?: boolean;
  addButtonTitle?: string;
  isThreadAttachment?: boolean;
  hasLowStrike?: boolean;
  wrapperClassNames?: Partial<typeof DEFAULT_WRAPPER_CLASS_NAMES>;
}

export default AttachmentField;
