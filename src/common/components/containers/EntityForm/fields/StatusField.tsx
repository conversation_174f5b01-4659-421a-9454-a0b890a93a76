import React, { useMemo } from 'react';

import FormStatusSelector from '../../../controls/FormStatusSelector';
import StatusValueView from '../../../controls/ValueViews/StatusValueView';
import EntityField, { IEntityFieldBasicProps } from '../internal/EntityField';
import useT from '../../../utils/Translations/useT';

export type TStatus = { name: string; value: string };

export interface IStatusField
  extends Partial<IEntityFieldBasicProps<string | unknown>> {
  isNew?: boolean;
  defaultValue?: string;
  hasOnlyActiveStatus?: boolean;
  hasOmitDeleteStatus?: boolean;
  label?: string;
  options?: Array<TStatus>;
  withTitle?: boolean;
  buttonClassNames?: string;
}

const StatusField: React.FC<IStatusField> = ({
  name = 'status',
  isNew = false,
  hasOnlyActiveStatus = false,
  hasOmitDeleteStatus = false,
  defaultValue = '',
  required = true,
  label,
  withTitle,
  buttonClassNames,
  ...rest
}) => {
  const t = useT();

  const _label = useMemo(() => label || t('Status'), [t, label]);

  return (
    <EntityField name={name} required={required} {...rest}>
      {(editable, field, { required, disabled }) =>
        editable ? (
          <FormStatusSelector
            buttonClassNames={buttonClassNames}
            defaultValue={defaultValue}
            disabled={disabled}
            hasOmitDeleteStatus={hasOmitDeleteStatus}
            hasOnlyActiveStatus={hasOnlyActiveStatus}
            isNew={isNew}
            label={_label}
            required={required}
            withTitle={withTitle}
            {...field}
          />
        ) : (
          <StatusValueView value={field.input.value || defaultValue} />
        )
      }
    </EntityField>
  );
};

export default StatusField;
