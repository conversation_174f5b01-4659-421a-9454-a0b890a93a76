import React, { useMemo } from 'react';

import { useTranslation } from '../../../../utils/Translations';
import Error from '../../../../utils/Error';
import useStaffPeople from '../../../../../data/hooks/useStaffPeople';
import MultiSelectBoxField from '../MultiSelectBoxField';

export default function PeopleMultiSelectField({ name }) {
  const { t } = useTranslation();

  const { loading, error, people } = useStaffPeople();

  const title = useMemo(
    () => (loading ? t('Loading Assignees...') : t('Assignee')),
    [t, loading],
  );

  if (error) {
    return <Error error={error} />;
  }

  return (
    <MultiSelectBoxField
      isLiveSearch
      noLabel
      disabled={loading}
      isRequired={false}
      itemTitlePropName="fullName"
      itemValuePropName="id"
      label={title}
      name={name}
      options={people}
    />
  );
}
