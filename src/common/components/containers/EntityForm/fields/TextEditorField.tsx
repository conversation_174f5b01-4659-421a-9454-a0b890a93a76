import React, { useCallback, useMemo, ReactNode, forwardRef } from 'react';
import { isEmpty } from 'lodash';
import { useQuery } from 'react-apollo';

import useReplaceContent from '../../../../data/hooks/useReplaceContent';
import FormTextEditor from '../../../controls/FormTextEditor';
import { useDependsOnFields } from '../index';
import EntityField from '../internal/EntityField';
import useT from '../../../utils/Translations/useT';
import { DEFAULT_WRAPPER_CLASS_NAMES } from '../internal/EntityFieldContent';
import storageSettingQueryGql from '../../../../../modules/System/StorageManagementPage/graphql/queries/storageSetting.graphql';
import useCurrentUser from '../../../../data/hooks/useCurrentUser';
import { ICustomButton } from '../../../controls/base/TextEditor/useCustomButtons';
import { TextEditorRef } from '../../../controls/base/TextEditor/TextEditor';

const MAX_LENGTH = 10000;
const ROW_HEIGHT = 32.4;
const ROW_VIEW_HEIGHT = 28;
const DEFAULT_HEIGHT = 300;
// eslint-disable-next-line no-magic-numbers
const PADDING = ROW_HEIGHT + 10;
const BYTES_IN_MEGABYTE = 1048576;

export interface ITextEditorField {
  name: string;
  label?: string;
  config?: object;
  maxLength?: number;
  isReadOnly?: boolean;
  noLabel?: boolean;
  columns?: number;
  rows?: number;
  hasMergeCodes?: boolean;
  hasMergeCodesRendered?: boolean;
  required?: boolean;
  moduleAreaKey?: string;
  entityId?: string;
  factKey?: string;
  isExtended?: boolean;
  fileCategory?: string;
  isErrorMessageLiftedUp?: boolean;
  hasMoreButton?: boolean;
  customLabel?: ReactNode;
  validate?: Function;
  wrapperClassNames?: Partial<typeof DEFAULT_WRAPPER_CLASS_NAMES>;
  clobsPreviewData?: Record<string, any>;
  cookedAttributes?: any;
  hasRenderedPreview?: boolean;
  customButtons?: ICustomButton[];
}

interface ILabelNotRequired extends ITextEditorField {
  noLabel: true;
  label?: string;
}

interface ILabelRequired extends ITextEditorField {
  noLabel?: false;
  label: string;
}

interface IMergeCodesRendered extends ITextEditorField {
  hasMergeCodesRendered: true;
  entityId: string;
  factKey: string;
}

interface IMergeCodesNotRendered extends ITextEditorField {
  hasMergeCodesRendered?: false;
  entityId?: string;
  factKey?: string;
}

export type TTextEditorField = (ILabelNotRequired | ILabelRequired) &
  (IMergeCodesRendered | IMergeCodesNotRendered);

const TextEditorField = forwardRef<TextEditorRef, TTextEditorField>(
  (
    {
      maxLength = MAX_LENGTH,
      isReadOnly = false,
      config = {},
      name,
      label,
      noLabel = false,
      hasMergeCodesRendered = false,
      entityId = '',
      factKey,
      isExtended,
      isErrorMessageLiftedUp,
      rows,
      required,
      validate,
      clobsPreviewData,
      cookedAttributes,
      hasRenderedPreview,
      customButtons,
      ...rest
    },
    ref,
  ) => {
    const t = useT();
    const {
      me: { tenantId },
    } = useCurrentUser();

    const { data } = useQuery(storageSettingQueryGql, {
      variables: {
        id: [
          {
            key: 'categoryKey',
            value: rest.fileCategory,
          },
          {
            key: 'tenantId',
            value: tenantId,
          },
        ],
      },
      skip: isEmpty(rest.fileCategory),
    });

    const fsCatMaxFileSize = useMemo(() => data?.setting?.maxFileSize, [data]);

    const _validate = useCallback(
      value => {
        if (validate && validate(value)) {
          return validate(value);
        }

        if (!value && required) {
          return 'Required';
        }

        return '';
      },
      [required],
    );

    const { content } = useDependsOnFields<{ content?: string }>({
      content: name,
    });

    const { renderedContent } = useReplaceContent({
      entityId,
      content,
      factKey,
    });

    const maybeRenderedContent = useMemo(
      () => (hasMergeCodesRendered ? renderedContent || content : content),
      [hasMergeCodesRendered, renderedContent, content],
    );

    const editConfig = useMemo(
      () => ({
        ...(~~fsCatMaxFileSize
          ? { imageMaxSize: fsCatMaxFileSize / BYTES_IN_MEGABYTE }
          : {}),
        heightMin: rows ? rows * ROW_HEIGHT + PADDING : undefined,
        ...(~~maxLength ? { charCounterMax: maxLength } : {}),
        ...config,
        placeholderText: label,
        paragraphStyles: {
          'fr-text-gray': t('Gray'),
          'fr-text-bordered': t('Bordered'),
          'fr-text-spaced': t('Spaced'),
          'fr-text-uppercase': t('Uppercase'),
        },
        inlineStyles: {
          [t('Big Red')]: 'font-size: 20px; color: red;',
          [t('Small Blue')]: 'font-size: 14px; color: blue;',
        },
      }),
      [rows, maxLength, config, label, t, fsCatMaxFileSize],
    );

    const viewConfig = useMemo(
      () => ({
        heightMin: rows ? rows * ROW_VIEW_HEIGHT + PADDING : DEFAULT_HEIGHT,
        toolbarButtons: [],
        quickInsertTags: [''],
        toolbarBottom: true,
        immediateReactModelUpdate: false,
        placeholderText: label,
      }),
      [rows, label],
    );

    return (
      <EntityField<string>
        name={name}
        required={required}
        validate={_validate}
        {...rest}
      >
        {(editing, { meta, ...field }, { required: isRequired, disabled }) =>
          !isReadOnly && editing ? (
            <FormTextEditor
              ref={ref}
              animateTitle={!noLabel}
              config={editConfig}
              customButtons={customButtons}
              isDisabled={disabled}
              isErrorMessageLiftedUp={isErrorMessageLiftedUp}
              isExtended={isExtended}
              isRequired={isRequired}
              label={label}
              meta={meta}
              name={name}
              {...field}
            />
          ) : (
            <FormTextEditor
              ref={ref}
              isDisabled
              animateTitle={!noLabel}
              config={viewConfig}
              isRequired={isRequired}
              label={label}
              meta={meta}
              {...field}
              clobsPreviewData={clobsPreviewData}
              cookedAttributes={cookedAttributes}
              customButtons={customButtons}
              hasRenderedPreview={hasRenderedPreview}
              input={{
                ...field.input,
                value: isEmpty(maybeRenderedContent)
                  ? t('None')
                  : maybeRenderedContent,
              }}
            />
          )
        }
      </EntityField>
    );
  },
);

export default TextEditorField;
